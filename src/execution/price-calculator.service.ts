import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';

@Injectable()
export class PriceCalculatorService {
  private readonly logger = new Logger(PriceCalculatorService.name);

  calculateUniswapV2Price(reserveA: bigint, reserveB: bigint, amountIn: bigint): bigint {
    // Uniswap V2 constant product formula: x * y = k
    // amountOut = (amountIn * 997 * reserveOut) / (reserveIn * 1000 + amountIn * 997)
    
    const amountInWithFee = amountIn * 997n;
    const numerator = amountInWithFee * reserveB;
    const denominator = reserveA * 1000n + amountInWithFee;
    
    return numerator / denominator;
  }

  calculateUniswapV3Price(sqrtPriceX96: bigint, liquidity: bigint, amountIn: bigint): bigint {
    // Simplified Uniswap V3 price calculation
    // In practice, this would be much more complex with tick math
    
    const price = (sqrtPriceX96 * sqrtPriceX96) / (2n ** 192n);
    return amountIn * price;
  }

  calculatePriceImpact(reserveIn: bigint, amountIn: bigint): number {
    // Price impact = amountIn / (reserveIn + amountIn)
    const impact = Number(amountIn) / (Number(reserveIn) + Number(amountIn));
    return impact * 100; // Return as percentage
  }

  calculateSlippage(expectedAmount: bigint, actualAmount: bigint): number {
    // Slippage = (expected - actual) / expected * 100
    const slippage = (Number(expectedAmount) - Number(actualAmount)) / Number(expectedAmount);
    return slippage * 100; // Return as percentage
  }
}
