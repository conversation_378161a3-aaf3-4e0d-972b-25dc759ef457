import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';
import { Pool } from '../types';

@Injectable()
export class PoolManagerService {
  private readonly logger = new Logger(PoolManagerService.name);
  
  private provider: ethers.JsonRpcProvider;
  private poolCache: Map<string, Pool> = new Map();
  private readonly CACHE_DURATION = 60000; // 1 minute

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
  }

  async getPool(
    tokenA: string, 
    tokenB: string, 
    protocol: 'uniswap-v2' | 'uniswap-v3' | 'sushiswap', 
    fee?: number
  ): Promise<Pool | null> {
    try {
      const cacheKey = `${protocol}-${tokenA}-${tokenB}-${fee || 0}`;
      
      // Check cache first
      const cached = this.poolCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
        return cached;
      }

      this.logger.debug(`Fetching pool: ${protocol} ${tokenA}/${tokenB}${fee ? ` (${fee})` : ''}`);

      let pool: Pool | null = null;

      switch (protocol) {
        case 'uniswap-v2':
          pool = await this.getUniswapV2Pool(tokenA, tokenB);
          break;
        case 'uniswap-v3':
          pool = await this.getUniswapV3Pool(tokenA, tokenB, fee || 3000);
          break;
        case 'sushiswap':
          pool = await this.getSushiswapPool(tokenA, tokenB);
          break;
        default:
          this.logger.warn(`Unknown protocol: ${protocol}`);
          return null;
      }

      if (pool) {
        pool.timestamp = Date.now();
        this.poolCache.set(cacheKey, pool);
      }

      return pool;

    } catch (error) {
      this.logger.error(`Error getting pool for ${tokenA}/${tokenB}:`, error);
      return null;
    }
  }

  private async getUniswapV2Pool(tokenA: string, tokenB: string): Promise<Pool | null> {
    try {
      // Uniswap V2 factory address
      const factoryAddress = '0x5C69bEe701ef814a2B6a3EDD4B1652CB9cc5aA6f';
      
      // Calculate pair address
      const pairAddress = this.calculateUniswapV2PairAddress(tokenA, tokenB, factoryAddress);
      
      // Get pair contract
      const pairABI = [
        'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
        'function token0() external view returns (address)',
        'function token1() external view returns (address)'
      ];
      
      const pairContract = new ethers.Contract(pairAddress, pairABI, this.provider);
      
      // Get reserves
      const [reserve0, reserve1] = await pairContract.getReserves();
      const token0 = await pairContract.token0();
      const token1 = await pairContract.token1();

      return {
        address: pairAddress,
        token0: { address: token0, symbol: 'TOKEN0', decimals: 18, name: 'Token 0' },
        token1: { address: token1, symbol: 'TOKEN1', decimals: 18, name: 'Token 1' },
        protocol: 'uniswap-v2',
        reserves: {
          reserve0: BigInt(reserve0.toString()),
          reserve1: BigInt(reserve1.toString())
        },
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.debug(`Uniswap V2 pool not found for ${tokenA}/${tokenB}`);
      return null;
    }
  }

  private async getUniswapV3Pool(tokenA: string, tokenB: string, fee: number): Promise<Pool | null> {
    try {
      // Uniswap V3 factory address
      const factoryAddress = '******************************************';
      
      const factoryABI = [
        'function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool)'
      ];
      
      const factoryContract = new ethers.Contract(factoryAddress, factoryABI, this.provider);
      
      // Get pool address
      const poolAddress = await factoryContract.getPool(tokenA, tokenB, fee);
      
      if (poolAddress === ethers.ZeroAddress) {
        return null;
      }

      // Get pool data
      const poolABI = [
        'function liquidity() external view returns (uint128)',
        'function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)'
      ];
      
      const poolContract = new ethers.Contract(poolAddress, poolABI, this.provider);
      
      const liquidity = await poolContract.liquidity();
      const slot0 = await poolContract.slot0();

      return {
        address: poolAddress,
        token0: { address: tokenA, symbol: 'TOKEN0', decimals: 18, name: 'Token 0' },
        token1: { address: tokenB, symbol: 'TOKEN1', decimals: 18, name: 'Token 1' },
        protocol: 'uniswap-v3',
        fee,
        liquidity: BigInt(liquidity.toString()),
        sqrtPriceX96: BigInt(slot0.sqrtPriceX96.toString()),
        tick: slot0.tick,
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.debug(`Uniswap V3 pool not found for ${tokenA}/${tokenB} fee ${fee}`);
      return null;
    }
  }

  private async getSushiswapPool(tokenA: string, tokenB: string): Promise<Pool | null> {
    try {
      // SushiSwap uses same factory pattern as Uniswap V2
      const factoryAddress = '******************************************';
      
      const pairAddress = this.calculateUniswapV2PairAddress(tokenA, tokenB, factoryAddress);
      
      // Same ABI as Uniswap V2
      const pairABI = [
        'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
        'function token0() external view returns (address)',
        'function token1() external view returns (address)'
      ];
      
      const pairContract = new ethers.Contract(pairAddress, pairABI, this.provider);
      
      const [reserve0, reserve1] = await pairContract.getReserves();
      const token0 = await pairContract.token0();
      const token1 = await pairContract.token1();

      return {
        address: pairAddress,
        token0: { address: token0, symbol: 'TOKEN0', decimals: 18, name: 'Token 0' },
        token1: { address: token1, symbol: 'TOKEN1', decimals: 18, name: 'Token 1' },
        protocol: 'sushiswap',
        reserves: {
          reserve0: BigInt(reserve0.toString()),
          reserve1: BigInt(reserve1.toString())
        },
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.debug(`SushiSwap pool not found for ${tokenA}/${tokenB}`);
      return null;
    }
  }

  private calculateUniswapV2PairAddress(tokenA: string, tokenB: string, factoryAddress: string): string {
    // Sort tokens
    const [token0, token1] = tokenA.toLowerCase() < tokenB.toLowerCase() ? [tokenA, tokenB] : [tokenB, tokenA];
    
    // Calculate CREATE2 address
    const salt = ethers.keccak256(ethers.solidityPacked(['address', 'address'], [token0, token1]));
    const initCodeHash = '0x96e8ac4277198ff8b6f785478aa9a39f403cb768dd02cbee326c3e7da348845f'; // Uniswap V2 init code hash
    
    return ethers.getCreate2Address(factoryAddress, salt, initCodeHash);
  }

  clearCache(): void {
    this.poolCache.clear();
    this.logger.log('Pool cache cleared');
  }

  getCacheSize(): number {
    return this.poolCache.size;
  }
}
