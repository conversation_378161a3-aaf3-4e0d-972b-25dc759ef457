#!/usr/bin/env node

/**
 * Script to help convert remaining classes to NestJS services
 * Usage: node scripts/convert-to-nestjs.js
 */

const fs = require('fs');
const path = require('path');

// Files that need to be converted
const conversions = [
  // Strategy services
  { from: 'src/strategies/sandwich.ts', to: 'src/strategies/sandwich-strategy.service.ts', className: 'SandwichStrategy', serviceName: 'SandwichStrategyService' },
  { from: 'src/strategies/frontrunning.ts', to: 'src/strategies/frontrunning-strategy.service.ts', className: 'FrontrunningStrategy', serviceName: 'FrontrunningStrategyService' },
  { from: 'src/strategies/multi-block.ts', to: 'src/strategies/multi-block-strategy.service.ts', className: 'MultiBlockStrategy', serviceName: 'MultiBlockStrategyService' },
  { from: 'src/strategies/uniswap-v3-flash.ts', to: 'src/strategies/uniswap-v3-flash-strategy.service.ts', className: 'UniswapV3FlashSwapStrategy', serviceName: 'UniswapV3FlashSwapStrategyService' },
  { from: 'src/strategies/dynamic-flashloan.ts', to: 'src/strategies/dynamic-flashloan-strategy.service.ts', className: 'DynamicFlashloanStrategy', serviceName: 'DynamicFlashloanStrategyService' },
  { from: 'src/strategies/mev-share-flashloan.ts', to: 'src/strategies/mev-share-flashloan-strategy.service.ts', className: 'MEVShareFlashloanStrategy', serviceName: 'MEVShareFlashloanStrategyService' },
  { from: 'src/strategies/balancer-flashloan.ts', to: 'src/strategies/balancer-flashloan-strategy.service.ts', className: 'BalancerFlashloanStrategy', serviceName: 'BalancerFlashloanStrategyService' },
  
  // Utility services
  { from: 'src/utils/logger.ts', to: 'src/utils/logger.service.ts', className: 'Logger', serviceName: 'LoggerService' },
  { from: 'src/utils/enhancedLogger.ts', to: 'src/utils/enhanced-logger.service.ts', className: 'EnhancedLogger', serviceName: 'EnhancedLoggerService' },
  { from: 'src/utils/statusDashboard.ts', to: 'src/utils/status-dashboard.service.ts', className: 'StatusDashboard', serviceName: 'StatusDashboardService' },
  { from: 'src/utils/performance-monitor.ts', to: 'src/utils/performance-monitor.service.ts', className: 'PerformanceMonitor', serviceName: 'PerformanceMonitorService' },
  
  // Event monitoring services
  { from: 'src/events/blockEventMonitor.ts', to: 'src/events/block-event-monitor.service.ts', className: 'BlockEventMonitor', serviceName: 'BlockEventMonitorService' },
  { from: 'src/mev-share/event-monitor.ts', to: 'src/mev-share/mev-share-event-monitor.service.ts', className: 'MEVShareEventMonitor', serviceName: 'MEVShareEventMonitorService' },
  
  // Execution services
  { from: 'src/execution/flashbots-executor.ts', to: 'src/execution/flashbots-executor.service.ts', className: 'FlashbotsExecutor', serviceName: 'FlashbotsExecutorService' },
  { from: 'src/execution/price-calculator.ts', to: 'src/execution/price-calculator.service.ts', className: 'PriceCalculator', serviceName: 'PriceCalculatorService' },
  { from: 'src/execution/swap-data-builder.ts', to: 'src/execution/swap-data-builder.service.ts', className: 'SwapDataBuilder', serviceName: 'SwapDataBuilderService' },
  
  // Gas services
  { from: 'src/gas/optimizer.ts', to: 'src/gas/gas-optimizer.service.ts', className: 'GasOptimizer', serviceName: 'GasOptimizerService' },
  { from: 'src/gas/advanced-estimator.ts', to: 'src/gas/advanced-gas-estimator.service.ts', className: 'AdvancedGasEstimator', serviceName: 'AdvancedGasEstimatorService' },
  
  // DEX services
  { from: 'src/dex/pools.ts', to: 'src/dex/pool-manager.service.ts', className: 'PoolManager', serviceName: 'PoolManagerService' },
  { from: 'src/dex/curve.ts', to: 'src/dex/curve-swapper.service.ts', className: 'CurveSwapper', serviceName: 'CurveSwapperService' },
  
  // Other services
  { from: 'src/simulation/simulator.ts', to: 'src/simulation/bundle-simulator.service.ts', className: 'BundleSimulator', serviceName: 'BundleSimulatorService' },
  { from: 'src/flashbots/bundle-provider.ts', to: 'src/flashbots/flashbots-bundle-manager.service.ts', className: 'FlashbotsBundleManager', serviceName: 'FlashbotsBundleManagerService' },
  { from: 'src/contracts/flashloan.ts', to: 'src/contracts/flashloan-contract.service.ts', className: 'FlashloanContractInterface', serviceName: 'FlashloanContractService' },
  { from: 'src/workers/worker-manager.ts', to: 'src/workers/worker-manager.service.ts', className: 'WorkerManager', serviceName: 'WorkerManagerService' },
  { from: 'src/workers/arbitrage-worker.ts', to: 'src/workers/arbitrage-worker.service.ts', className: 'ArbitrageWorker', serviceName: 'ArbitrageWorkerService' },
];

function generateServiceTemplate(className, serviceName, originalContent) {
  // Extract imports and class content
  const lines = originalContent.split('\n');
  const imports = [];
  const classContent = [];
  let inClass = false;
  let classStartLine = -1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    if (line.startsWith('import ') || line.startsWith('export ')) {
      if (!line.includes('from \'../config\'') && !line.includes('from \'../config/index\'')) {
        imports.push(line);
      }
    } else if (line.includes(`export class ${className}`)) {
      inClass = true;
      classStartLine = i;
    } else if (inClass) {
      classContent.push(line);
    }
  }

  // Generate NestJS service template
  const template = `import { Injectable, Logger } from '@nestjs/common';
import { ConfigurationService } from '../config/configuration.service';
${imports.filter(imp => !imp.includes('config')).join('\n')}

@Injectable()
export class ${serviceName} {
  private readonly logger = new Logger(${serviceName}.name);
  
  constructor(
    private readonly configService: ConfigurationService,
    // TODO: Add other required dependencies
  ) {
    // TODO: Move constructor logic here
    // Replace 'config.' with 'this.configService.'
  }
  
  // TODO: Convert all methods from original ${className} class
  ${classContent.slice(0, 20).join('\n')}
  
  // ... rest of the methods need to be converted manually
}`;

  return template;
}

function createConversionStub(conversion) {
  const { from, to, className, serviceName } = conversion;
  
  console.log(`Creating conversion stub: ${from} -> ${to}`);
  
  try {
    // Check if source file exists
    if (!fs.existsSync(from)) {
      console.warn(`Source file not found: ${from}`);
      return;
    }
    
    // Check if target already exists
    if (fs.existsSync(to)) {
      console.log(`Target file already exists: ${to}`);
      return;
    }
    
    // Read original file
    const originalContent = fs.readFileSync(from, 'utf8');
    
    // Generate service template
    const serviceContent = generateServiceTemplate(className, serviceName, originalContent);
    
    // Write service file
    fs.writeFileSync(to, serviceContent);
    console.log(`✅ Created: ${to}`);
    
  } catch (error) {
    console.error(`❌ Error creating ${to}:`, error.message);
  }
}

function updateModuleExports() {
  console.log('\n📝 Module export updates needed:');
  console.log('Update the following module files to include new services:');
  
  const moduleUpdates = {
    'src/strategies/strategies.module.ts': [
      'SandwichStrategyService',
      'FrontrunningStrategyService', 
      'MultiBlockStrategyService',
      'UniswapV3FlashSwapStrategyService',
      'DynamicFlashloanStrategyService',
      'MEVShareFlashloanStrategyService',
      'BalancerFlashloanStrategyService'
    ],
    'src/utils/utils.module.ts': [
      'LoggerService',
      'EnhancedLoggerService',
      'StatusDashboardService',
      'PerformanceMonitorService'
    ],
    'src/monitoring/monitoring.module.ts': [
      'BlockEventMonitorService',
      'MEVShareEventMonitorService'
    ],
    'src/execution/execution.module.ts': [
      'FlashbotsExecutorService',
      'PriceCalculatorService',
      'SwapDataBuilderService'
    ],
    'src/gas/gas.module.ts': [
      'GasOptimizerService',
      'AdvancedGasEstimatorService'
    ],
    'src/dex/dex.module.ts': [
      'PoolManagerService',
      'CurveSwapperService'
    ],
    'src/simulation/simulation.module.ts': [
      'BundleSimulatorService'
    ],
    'src/flashbots/flashbots.module.ts': [
      'FlashbotsBundleManagerService'
    ],
    'src/contracts/contracts.module.ts': [
      'FlashloanContractService'
    ],
    'src/workers/workers.module.ts': [
      'WorkerManagerService',
      'ArbitrageWorkerService'
    ]
  };
  
  Object.entries(moduleUpdates).forEach(([moduleFile, services]) => {
    console.log(`\n${moduleFile}:`);
    services.forEach(service => {
      console.log(`  - Add ${service} to providers and exports`);
    });
  });
}

function main() {
  console.log('🚀 NestJS Conversion Helper\n');
  
  console.log('Creating service conversion stubs...\n');
  
  // Create conversion stubs
  conversions.forEach(createConversionStub);
  
  console.log('\n✅ Conversion stubs created!');
  console.log('\n📋 Next steps:');
  console.log('1. Review each generated service file');
  console.log('2. Complete the TODO items in each service');
  console.log('3. Update module exports (see below)');
  console.log('4. Test the converted services');
  console.log('5. Update imports in dependent files');
  
  updateModuleExports();
  
  console.log('\n🔧 Manual conversion required for:');
  console.log('- Constructor dependency injection');
  console.log('- Replace config imports with ConfigurationService');
  console.log('- Convert event emitters to NestJS EventEmitter2');
  console.log('- Add proper error handling and logging');
  
  console.log('\n📖 See NESTJS_CONVERSION_GUIDE.md for detailed instructions');
}

if (require.main === module) {
  main();
}
