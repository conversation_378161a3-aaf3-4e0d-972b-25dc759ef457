import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { FlashbotsBundleManagerService } from '../flashbots/flashbots-bundle-manager.service';
import { GasOptimizerService } from '../gas/gas-optimizer.service';

@Injectable()
export class FlashbotsExecutorService {
  private readonly logger = new Logger(FlashbotsExecutorService.name);

  constructor(
    private readonly configService: ConfigurationService,
    private readonly flashbotsManager: FlashbotsBundleManagerService,
    private readonly gasOptimizer: GasOptimizerService,
  ) {}

  async executeTransaction(transaction: any): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      if (this.configService.dryRun) {
        this.logger.log('🧪 DRY RUN: Would execute transaction', {
          to: transaction.to,
          value: transaction.value?.toString(),
          data: transaction.data?.slice(0, 10) + '...'
        });
        return { success: true, txHash: '0xdryrun...' };
      }

      this.logger.log('⚡ Executing transaction via Flashbots');

      // Create bundle with single transaction
      const bundleTransactions = [{
        transaction,
        signer: new ethers.Wallet(this.configService.privateKey)
      }];

      // Submit bundle
      const result = await this.flashbotsManager.submitBundle(bundleTransactions);
      
      if (result.success) {
        this.logger.log('✅ Transaction executed successfully', {
          bundleHash: result.bundleHash
        });
      }

      return result;

    } catch (error) {
      this.logger.error('❌ Transaction execution failed:', error);
      return { success: false, error: error.message };
    }
  }

  async executeBundle(transactions: any[]): Promise<{ success: boolean; bundleHash?: string; error?: string }> {
    try {
      if (this.configService.dryRun) {
        this.logger.log('🧪 DRY RUN: Would execute bundle', {
          transactionCount: transactions.length
        });
        return { success: true, bundleHash: '0xdryrun...' };
      }

      this.logger.log('📦 Executing bundle via Flashbots', {
        transactionCount: transactions.length
      });

      // Create bundle transactions
      const bundleTransactions = transactions.map(tx => ({
        transaction: tx,
        signer: new ethers.Wallet(this.configService.privateKey)
      }));

      // Submit bundle
      const result = await this.flashbotsManager.submitBundle(bundleTransactions);
      
      if (result.success) {
        this.logger.log('✅ Bundle executed successfully', {
          bundleHash: result.bundleHash
        });
      }

      return result;

    } catch (error) {
      this.logger.error('❌ Bundle execution failed:', error);
      return { success: false, error: error.message };
    }
  }

  async simulateExecution(transactions: any[]): Promise<{ success: boolean; gasUsed?: bigint; error?: string }> {
    try {
      this.logger.debug('🧪 Simulating execution');

      // Create bundle transactions for simulation
      const bundleTransactions = transactions.map(tx => ({
        transaction: tx,
        signer: new ethers.Wallet(this.configService.privateKey)
      }));

      // Simulate bundle
      const result = await this.flashbotsManager.simulateBundle(bundleTransactions);
      
      if (result.success) {
        this.logger.debug('✅ Simulation successful', {
          gasUsed: result.gasUsed?.toString()
        });
      }

      return result;

    } catch (error) {
      this.logger.error('❌ Simulation failed:', error);
      return { success: false, error: error.message };
    }
  }

  isAvailable(): boolean {
    return this.flashbotsManager.isEnabled();
  }
}
