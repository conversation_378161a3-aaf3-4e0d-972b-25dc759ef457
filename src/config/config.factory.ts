import { registerAs } from '@nestjs/config';

export const configFactory = registerAs('app', () => {
  const isHardhat = process.env.HARDHAT === 'true';
  
  return {
    // Network configuration
    chainId: parseInt(process.env.CHAIN_ID || '11155111'),
    rpcUrl: process.env.RPC_URL || 'http://localhost:8545',
    privateKey: process.env.PRIVATE_KEY || '',
    flashbotsSignerKey: process.env.FLASHBOTS_SIGNER_KEY || '',
    
    // MEV configuration
    minProfitWei: process.env.MIN_PROFIT_WEI || '100000000000000',
    maxGasPriceGwei: parseInt(process.env.MAX_GAS_PRICE_GWEI || '100'),
    maxPriorityFeeGwei: parseInt(process.env.MAX_PRIORITY_FEE_GWEI || '5'),
    slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.005'),
    
    // Mempool monitoring
    mempoolWebsocketUrl: process.env.MEMPOOL_WEBSOCKET_URL || '',
    enableFlashbotsMempool: process.env.ENABLE_FLASHBOTS_MEMPOOL === 'true',
    enableEthersMempool: process.env.ENABLE_ETHERS_MEMPOOL === 'true',
    
    // Strategy configuration
    enableSandwichAttacks: process.env.ENABLE_SANDWICH_ATTACKS === 'true',
    enableFrontRunning: process.env.ENABLE_FRONT_RUNNING === 'true',
    enableArbitrage: process.env.ENABLE_ARBITRAGE === 'true',
    enableFlashloanAttacks: process.env.ENABLE_FLASHLOAN_ATTACKS === 'true',
    enableMultiBlockAttacks: process.env.ENABLE_MULTI_BLOCK_ATTACKS === 'true',
    maxBlocksAhead: parseInt(process.env.MAX_BLOCKS_AHEAD || '3'),
    
    // Risk management
    maxPositionSizeEth: parseFloat(process.env.MAX_POSITION_SIZE_ETH || '10'),
    emergencyStop: process.env.EMERGENCY_STOP === 'true',
    dryRun: process.env.DRY_RUN === 'true',
    
    // Flashloan contract addresses
    hybridFlashloanContract: process.env.HYBRID_FLASHLOAN_CONTRACT || '',
    balancerFlashloanContract: process.env.BALANCER_FLASHLOAN_CONTRACT || '',
    aaveFlashloanContract: process.env.AAVE_FLASHLOAN_CONTRACT || '',
    
    // Flashloan DEX configuration
    flashloanDexPairs: (process.env.FLASHLOAN_DEX_PAIRS || 'UNISWAP_V2,UNISWAP_V3').split(','),
    flashloanBuyDex: process.env.FLASHLOAN_BUY_DEX || 'UNISWAP_V2',
    flashloanSellDex: process.env.FLASHLOAN_SELL_DEX || 'UNISWAP_V3',
    enableCrossDexArbitrage: process.env.ENABLE_CROSS_DEX_ARBITRAGE === 'true',
    minArbitrageSpread: parseFloat(process.env.MIN_ARBITRAGE_SPREAD || '0.5'),
    
    // Flashloan token configuration
    flashloanTokens: (process.env.FLASHLOAN_TOKENS || 'USDC,WETH,USDT,DAI').split(','),
    flashloanPrimaryToken: process.env.FLASHLOAN_PRIMARY_TOKEN || 'USDC',
    flashloanTargetTokens: (process.env.FLASHLOAN_TARGET_TOKENS || 'WETH,USDT,DAI').split(','),
    enableAllTokenPairs: process.env.ENABLE_ALL_TOKEN_PAIRS === 'true',
    minTokenLiquidityUsd: parseFloat(process.env.MIN_TOKEN_LIQUIDITY_USD || '100000'),
    
    // Flashloan amount configuration
    flashloanBaseAmountWeth: parseFloat(process.env.FLASHLOAN_BASE_AMOUNT_WETH || '5'),
    flashloanBaseAmountUsdc: parseFloat(process.env.FLASHLOAN_BASE_AMOUNT_USDC || '20000'),
    flashloanMaxMultiplier: parseInt(process.env.FLASHLOAN_MAX_MULTIPLIER || '10'),
    
    // Flashbots configuration
    enableFlashbots: process.env.ENABLE_FLASHBOTS === 'true' && !isHardhat,
    flashbotsRelayUrl: process.env.FLASHBOTS_RELAY_URL || (isHardhat ? 'http://localhost:8545' : 'https://relay.flashbots.net'),
    flashbotsAuthKey: process.env.FLASHBOTS_AUTH_KEY || '',
    
    // Uniswap V3 Flash Swap configuration
    uniswapV3TradingPairs: (process.env.UNISWAP_V3_TRADING_PAIRS || 'WETH/USDC,WETH/USDT,WBTC/WETH,DAI/USDC').split(','),
    uniswapV3FeeTiers: (process.env.UNISWAP_V3_FEE_TIERS || '500,3000,10000').split(',').map(Number),
    
    // MEV-Share configuration
    enableMevShare: process.env.ENABLE_MEV_SHARE === 'true',
    mevShareStreamUrl: process.env.MEV_SHARE_STREAM_URL || 'https://mev-share.flashbots.net',
    enableBackrunStrategy: process.env.ENABLE_BACKRUN_STRATEGY === 'true',
    minBackrunProfitEth: parseFloat(process.env.MIN_BACKRUN_PROFIT_ETH || '0.01'),
    maxGasCostEth: parseFloat(process.env.MAX_GAS_COST_ETH || '0.01'),
    
    // Advanced gas estimation
    blocknativeApiKey: process.env.BLOCKNATIVE_API_KEY || '',
    enableBlocknativeGas: process.env.ENABLE_BLOCKNATIVE_GAS === 'true',
    enable0xApiGas: process.env.ENABLE_0X_API_GAS === 'true',
    enableEthGasStation: process.env.ENABLE_ETH_GAS_STATION === 'true',
    fallbackGasPriceGwei: parseInt(process.env.FALLBACK_GAS_PRICE_GWEI || '20'),
    
    // Scanning intervals
    arbitrageScanIntervalMs: parseInt(process.env.ARBITRAGE_SCAN_INTERVAL_MS || '0'),
    flashloanScanIntervalMs: parseInt(process.env.FLASHLOAN_SCAN_INTERVAL_MS || '0'),
    
    // Test mode configuration
    enableTestMode: process.env.ENABLE_TEST_MODE === 'true',
    mockOpportunities: process.env.MOCK_OPPORTUNITIES === 'true',
    testLiquiditySetup: process.env.TEST_LIQUIDITY_SETUP === 'true',
    
    // Logging
    logLevel: process.env.LOG_LEVEL || 'info',
    logToFile: process.env.LOG_TO_FILE === 'true',
  };
});
