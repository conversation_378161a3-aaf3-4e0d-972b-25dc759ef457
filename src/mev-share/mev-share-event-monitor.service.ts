import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigurationService } from '../config/configuration.service';

export interface BackrunOpportunity {
  userTxHash: string;
  estimatedProfit: bigint;
  gasEstimate: bigint;
  confidence: number;
  timestamp: number;
}

@Injectable()
export class MEVShareEventMonitorService {
  private readonly logger = new Logger(MEVShareEventMonitorService.name);
  
  private isRunning = false;
  private pendingOpportunities: BackrunOpportunity[] = [];

  constructor(
    private readonly configService: ConfigurationService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('MEV-Share event monitor is already running');
      return;
    }

    if (!this.configService.enableMevShare) {
      this.logger.log('MEV-Share is disabled, skipping monitor start');
      return;
    }

    this.logger.log('🔄 Starting MEV-Share event monitoring...');

    try {
      // Initialize MEV-Share connection
      await this.initializeMevShareConnection();
      
      this.isRunning = true;
      this.logger.log('✅ MEV-Share event monitoring started');
      this.eventEmitter.emit('mevShare.started');

    } catch (error) {
      this.logger.error('❌ Failed to start MEV-Share event monitoring:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('MEV-Share event monitor is not running');
      return;
    }

    this.logger.log('🛑 Stopping MEV-Share event monitoring...');

    try {
      this.isRunning = false;
      this.pendingOpportunities = [];
      
      this.logger.log('✅ MEV-Share event monitoring stopped');
      this.eventEmitter.emit('mevShare.stopped');

    } catch (error) {
      this.logger.error('❌ Error stopping MEV-Share event monitoring:', error);
      throw error;
    }
  }

  private async initializeMevShareConnection(): Promise<void> {
    try {
      // Initialize connection to MEV-Share stream
      // This would connect to the actual MEV-Share service
      
      this.logger.log('MEV-Share connection initialized');

    } catch (error) {
      this.logger.error('Failed to initialize MEV-Share connection:', error);
      throw error;
    }
  }

  async getPendingOpportunities(): Promise<BackrunOpportunity[]> {
    // Filter out expired opportunities (older than 30 seconds)
    const now = Date.now();
    this.pendingOpportunities = this.pendingOpportunities.filter(
      op => now - op.timestamp < 30000
    );

    return [...this.pendingOpportunities];
  }

  private addOpportunity(opportunity: BackrunOpportunity): void {
    this.pendingOpportunities.push(opportunity);
    
    // Keep only recent opportunities
    if (this.pendingOpportunities.length > 100) {
      this.pendingOpportunities = this.pendingOpportunities.slice(-100);
    }

    // Emit opportunity event
    this.eventEmitter.emit('mevShare.backrunOpportunity', opportunity);
  }

  getStatus(): { isRunning: boolean; pendingOpportunities: number } {
    return {
      isRunning: this.isRunning,
      pendingOpportunities: this.pendingOpportunities.length
    };
  }
}
