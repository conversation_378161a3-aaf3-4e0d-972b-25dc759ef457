import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';
import { Transaction } from '../types';

@Injectable()
export class MempoolMonitorService {
  private readonly logger = new Logger(MempoolMonitorService.name);
  
  private provider: ethers.WebSocketProvider | null = null;
  private isRunning = false;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;
  private readonly reconnectDelay = 5000; // 5 seconds

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Mempool monitor is already running');
      return;
    }

    this.logger.log('🔍 Starting mempool monitoring...');

    try {
      await this.initializeProvider();
      await this.setupEventListeners();
      
      this.isRunning = true;
      this.reconnectAttempts = 0;
      
      this.logger.log('✅ Mempool monitoring started');
      this.eventEmitter.emit('mempool.started');

    } catch (error) {
      this.logger.error('❌ Failed to start mempool monitoring:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Mempool monitor is not running');
      return;
    }

    this.logger.log('🛑 Stopping mempool monitoring...');

    try {
      this.isRunning = false;
      
      if (this.provider) {
        this.provider.removeAllListeners();
        this.provider.destroy();
        this.provider = null;
      }

      this.logger.log('✅ Mempool monitoring stopped');
      this.eventEmitter.emit('mempool.stopped');

    } catch (error) {
      this.logger.error('❌ Error stopping mempool monitoring:', error);
      throw error;
    }
  }

  private async initializeProvider(): Promise<void> {
    try {
      this.provider = this.rpcManager.getWebSocketProvider();
      
      // Test the connection
      await this.provider.getNetwork();
      this.logger.log('✅ WebSocket provider connected');

    } catch (error) {
      this.logger.error('❌ Failed to initialize WebSocket provider:', error);
      throw error;
    }
  }

  private async setupEventListeners(): Promise<void> {
    if (!this.provider) {
      throw new Error('Provider not initialized');
    }

    // Listen for pending transactions
    this.provider.on('pending', async (txHash: string) => {
      if (!this.isRunning) return;

      try {
        const transaction = await this.provider!.getTransaction(txHash);
        if (transaction) {
          await this.handlePendingTransaction(transaction);
        }
      } catch (error) {
        // Silently ignore individual transaction errors to avoid spam
        this.logger.debug(`Error fetching transaction ${txHash}:`, error);
      }
    });

    // Handle provider errors
    this.provider.on('error', (error) => {
      this.logger.error('WebSocket provider error:', error);
      this.handleProviderError(error);
    });

    // Handle connection close
    this.provider.on('close', (code, reason) => {
      this.logger.warn(`WebSocket connection closed: ${code} - ${reason}`);
      if (this.isRunning) {
        this.handleReconnection();
      }
    });

    this.logger.log('✅ Event listeners setup complete');
  }

  private async handlePendingTransaction(transaction: ethers.TransactionResponse): Promise<void> {
    try {
      // Filter relevant transactions
      if (this.isRelevantTransaction(transaction)) {
        const processedTransaction: Transaction = {
          hash: transaction.hash,
          from: transaction.from,
          to: transaction.to || '',
          value: transaction.value,
          gasPrice: transaction.gasPrice || 0n,
          gasLimit: transaction.gasLimit,
          data: transaction.data,
          nonce: transaction.nonce,
          timestamp: Date.now(),
        };

        // Emit event for other services to handle
        this.eventEmitter.emit('mempool.pendingTransaction', processedTransaction);
      }
    } catch (error) {
      this.logger.error('Error handling pending transaction:', error);
    }
  }

  private isRelevantTransaction(transaction: ethers.TransactionResponse): boolean {
    // Filter criteria for MEV-relevant transactions
    
    // Skip if no recipient (contract creation)
    if (!transaction.to) {
      return false;
    }

    // Skip if value is too low (likely not profitable)
    const minValue = ethers.parseEther('0.01'); // 0.01 ETH
    if (transaction.value < minValue) {
      return false;
    }

    // Skip if gas price is too low (likely won't be mined soon)
    const minGasPrice = ethers.parseUnits('10', 'gwei'); // 10 gwei
    if (transaction.gasPrice && transaction.gasPrice < minGasPrice) {
      return false;
    }

    // Check if transaction interacts with known DEX contracts
    const knownDexAddresses = this.getKnownDexAddresses();
    if (knownDexAddresses.includes(transaction.to.toLowerCase())) {
      return true;
    }

    // Check if transaction has significant data (likely a contract interaction)
    if (transaction.data && transaction.data.length > 10) { // More than just '0x'
      return true;
    }

    return false;
  }

  private getKnownDexAddresses(): string[] {
    // Return known DEX router addresses for filtering
    return [
      '******************************************', // Uniswap V2 Router
      '******************************************', // Uniswap V3 Router
      '******************************************', // SushiSwap Router
      '******************************************', // Balancer Vault
    ].map(addr => addr.toLowerCase());
  }

  private handleProviderError(error: any): void {
    this.logger.error('Provider error occurred:', error);
    this.eventEmitter.emit('mempool.error', error);
    
    if (this.isRunning) {
      this.handleReconnection();
    }
  }

  private async handleReconnection(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('Max reconnection attempts reached, stopping mempool monitoring');
      this.isRunning = false;
      this.eventEmitter.emit('mempool.maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    this.logger.warn(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

    try {
      // Wait before reconnecting
      await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));

      // Try to switch to a different provider
      await this.rpcManager.switchProvider();
      
      // Reinitialize
      await this.initializeProvider();
      await this.setupEventListeners();
      
      this.reconnectAttempts = 0;
      this.logger.log('✅ Reconnected successfully');
      this.eventEmitter.emit('mempool.reconnected');

    } catch (error) {
      this.logger.error('Reconnection failed:', error);
      // Will retry on next error or connection close
    }
  }

  getStatus(): { isRunning: boolean; reconnectAttempts: number; provider: string | null } {
    return {
      isRunning: this.isRunning,
      reconnectAttempts: this.reconnectAttempts,
      provider: this.rpcManager.getCurrentProvider()?.name || null,
    };
  }
}
