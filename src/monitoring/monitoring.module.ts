import { Module } from '@nestjs/common';
import { MempoolMonitorService } from '../mempool/mempool-monitor.service';
import { BlockEventMonitorService } from '../events/block-event-monitor.service';
import { MEVShareEventMonitorService } from '../mev-share/mev-share-event-monitor.service';

@Module({
  providers: [
    MempoolMonitorService,
    BlockEventMonitorService,
    MEVShareEventMonitorService,
  ],
  exports: [
    MempoolMonitorService,
    BlockEventMonitorService,
    MEVShareEventMonitorService,
  ],
})
export class MonitoringModule {}
