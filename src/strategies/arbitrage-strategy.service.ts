import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';
import { PoolManagerService } from '../dex/pool-manager.service';
import { GasOptimizerService } from '../gas/gas-optimizer.service';
import { WorkerManagerService } from '../workers/worker-manager.service';
import { ArbitrageRoute, Token, Transaction } from '../types';

@Injectable()
export class ArbitrageStrategyService {
  private readonly logger = new Logger(ArbitrageStrategyService.name);
  
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private readonly MIN_PROFIT_THRESHOLD = 0.005; // 0.5%
  private useWorkers = false;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
    private readonly poolManager: PoolManagerService,
    private readonly gasOptimizer: GasOptimizerService,
    private readonly eventEmitter: EventEmitter2,
    private readonly workerManager?: WorkerManagerService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
    this.wallet = new ethers.Wallet(this.configService.privateKey, this.provider);
    
    // Enable workers if available and configured
    this.useWorkers = !!this.workerManager && this.configService.get('enableWorkers', false);
    
    this.logger.log(`Arbitrage strategy initialized (workers: ${this.useWorkers ? 'enabled' : 'disabled'})`);
  }

  async scanForArbitrageOpportunities(): Promise<ArbitrageRoute[]> {
    try {
      this.logger.debug('Starting arbitrage scan', {
        useWorkers: this.useWorkers,
      });

      // Use worker-based scanning if enabled and available
      if (this.useWorkers && this.workerManager) {
        return await this.scanWithWorkers();
      } else {
        return await this.scanSingleThreaded();
      }

    } catch (error) {
      this.logger.error('Error scanning for arbitrage opportunities:', error);
      return [];
    }
  }

  async analyzeTransaction(transaction: Transaction): Promise<void> {
    try {
      // Analyze transaction for arbitrage opportunities
      this.logger.debug(`Analyzing transaction: ${transaction.hash}`);
      
      // Check if transaction affects token prices
      const affectedTokens = await this.getAffectedTokens(transaction);
      
      if (affectedTokens.length > 0) {
        // Scan for arbitrage opportunities on affected tokens
        const opportunities = await this.scanSpecificTokens(affectedTokens);
        
        for (const opportunity of opportunities) {
          this.eventEmitter.emit('opportunity.found', {
            type: 'arbitrage',
            route: opportunity,
            profitability: this.calculateProfitability(opportunity),
            confidence: 85,
          });
        }
      }

    } catch (error) {
      this.logger.error('Error analyzing transaction:', error);
    }
  }

  private async scanWithWorkers(): Promise<ArbitrageRoute[]> {
    if (!this.workerManager) {
      throw new Error('Worker manager not available');
    }

    try {
      // Generate all token pairs
      const tokenPairs = await this.generateTokenPairs();

      // Get current gas price for workers
      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      const gasPrice = gasStrategy.maxFeePerGas.toString();

      // Process token pairs using worker pool
      const result = await this.workerManager.processArbitrageScan(
        tokenPairs,
        this.MIN_PROFIT_THRESHOLD,
        gasPrice
      );

      this.logger.debug(`Worker-based arbitrage scan complete:`, {
        opportunities: result.opportunities.length,
        processedPairs: result.processedPairs,
        profitableCount: result.profitableCount,
        processingTime: `${result.processingTime}ms`
      });

      return result.opportunities;

    } catch (error) {
      this.logger.error('Worker-based arbitrage scan failed:', error);
      // Fallback to single-threaded scanning
      return await this.scanSingleThreaded();
    }
  }

  private async scanSingleThreaded(): Promise<ArbitrageRoute[]> {
    const opportunities: ArbitrageRoute[] = [];
    const startTime = Date.now();

    try {
      const tokens = await this.getConfiguredTokens();
      
      // Scan common token pairs across different protocols
      for (let i = 0; i < tokens.length; i++) {
        for (let j = i + 1; j < tokens.length; j++) {
          const token0 = tokens[i];
          const token1 = tokens[j];

          this.logger.debug('Scanning token pair', {
            token0: token0.symbol,
            token1: token1.symbol
          });

          // Check arbitrage between Uniswap V2 and V3
          const v2v3Arbitrage = await this.findV2V3Arbitrage(token0, token1);
          if (v2v3Arbitrage) {
            opportunities.push(v2v3Arbitrage);
            this.logger.debug('V2/V3 arbitrage found', {
              token0: token0.symbol,
              token1: token1.symbol,
              profit: ethers.formatEther(v2v3Arbitrage.expectedProfit)
            });
          }

          // Check other DEX combinations if enabled
          if (this.configService.get('enableCrossDexArbitrage', false)) {
            const crossDexOpportunities = await this.findCrossDexArbitrage(token0, token1);
            opportunities.push(...crossDexOpportunities);
          }
        }
      }

      const processingTime = Date.now() - startTime;
      const totalPairs = (tokens.length * (tokens.length - 1)) / 2;

      this.logger.debug(`Single-threaded arbitrage scan complete:`, {
        opportunities: opportunities.length,
        processedPairs: totalPairs,
        processingTime: `${processingTime}ms`,
        avgTimePerPair: `${(processingTime / totalPairs).toFixed(2)}ms`
      });

      return opportunities;

    } catch (error) {
      this.logger.error('Single-threaded arbitrage scan failed:', error);
      return opportunities;
    }
  }

  private async generateTokenPairs(): Promise<Array<{ token0: Token; token1: Token }>> {
    const tokens = await this.getConfiguredTokens();
    const tokenPairs: Array<{ token0: Token; token1: Token }> = [];
    
    for (let i = 0; i < tokens.length; i++) {
      for (let j = i + 1; j < tokens.length; j++) {
        tokenPairs.push({
          token0: tokens[i],
          token1: tokens[j]
        });
      }
    }
    
    return tokenPairs;
  }

  private async getConfiguredTokens(): Promise<Token[]> {
    // Get tokens from configuration service
    const tokenSymbols = this.configService.flashloanTokens;
    // Convert symbols to Token objects (implementation depends on token configuration)
    return []; // Placeholder - implement based on your token configuration
  }

  private async getAffectedTokens(transaction: Transaction): Promise<Token[]> {
    // Analyze transaction to determine which tokens might be affected
    // This would involve parsing transaction data and identifying token transfers/swaps
    return []; // Placeholder
  }

  private async scanSpecificTokens(tokens: Token[]): Promise<ArbitrageRoute[]> {
    // Scan for arbitrage opportunities on specific tokens
    return []; // Placeholder
  }

  private async findV2V3Arbitrage(token0: Token, token1: Token): Promise<ArbitrageRoute | null> {
    try {
      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v3', 3000);

      if (!v2Pool || !v3Pool) {
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, token0, token1);
      const v3Price = this.calculatePoolPrice(v3Pool, token0, token1);

      if (!v2Price || !v3Price) {
        return null;
      }

      // Check for price difference
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      if (priceDifference < this.MIN_PROFIT_THRESHOLD) {
        return null;
      }

      // Build arbitrage route
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;

      return {
        tokenA: token0,
        tokenB: token1,
        buyPool,
        sellPool,
        amount: ethers.parseEther('1'), // Calculate optimal amount
        expectedProfit: ethers.parseEther((priceDifference * 0.8).toString()), // Estimate profit
        gasEstimate: 200000n,
        confidence: 80,
      };

    } catch (error) {
      this.logger.error(`Error finding V2/V3 arbitrage for ${token0.symbol}/${token1.symbol}:`, error);
      return null;
    }
  }

  private async findCrossDexArbitrage(token0: Token, token1: Token): Promise<ArbitrageRoute[]> {
    // Implementation for cross-DEX arbitrage finding
    return []; // Placeholder
  }

  private calculatePoolPrice(pool: any, token0: Token, token1: Token): number | null {
    // Implementation for pool price calculation
    return null; // Placeholder
  }

  private calculateProfitability(route: ArbitrageRoute): number {
    // Calculate profitability score for the route
    const profit = Number(ethers.formatEther(route.expectedProfit));
    const gasEstimate = Number(route.gasEstimate);
    const gasCost = gasEstimate * 20e-9; // Estimate gas cost in ETH
    
    return Math.max(0, profit - gasCost);
  }

  async executeArbitrage(route: ArbitrageRoute): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      this.logger.log('Executing arbitrage route', {
        tokenA: route.tokenA.symbol,
        tokenB: route.tokenB.symbol,
        expectedProfit: ethers.formatEther(route.expectedProfit)
      });

      // Implementation for arbitrage execution
      // This would involve creating and submitting the arbitrage transaction

      return { success: true, txHash: '0x...' }; // Placeholder

    } catch (error) {
      this.logger.error('❌ Arbitrage execution failed:', error);
      return { success: false, error: error.message };
    }
  }
}
