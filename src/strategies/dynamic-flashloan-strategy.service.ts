import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';
import { FlashloanStrategyService } from './flashloan-strategy.service';
import { UniswapV3FlashSwapStrategyService } from './uniswap-v3-flash-strategy.service';
import { MEVShareFlashloanStrategyService } from './mev-share-flashloan-strategy.service';
import { FlashbotsBundleManagerService } from '../flashbots/flashbots-bundle-manager.service';
import { FlashbotsExecutorService } from '../execution/flashbots-executor.service';
import { FlashloanRoute, ArbitrageRoute, Pool, Token } from '../types';

/**
 * Strategy performance statistics
 */
interface StrategyStats {
  totalOpportunities: number;
  successfulExecutions: number;
  totalProfit: bigint;
  averageGasCost: bigint;
  averageExecutionTime: number;
  successRate: number;
  profitability: number;
}

/**
 * Enhanced flashloan route with strategy information
 */
interface EnhancedFlashloanRoute extends FlashloanRoute {
  strategy: 'aave' | 'balancer' | 'uniswap-v3' | 'mev-share';
  estimatedGasCost: bigint;
  netProfit: bigint;
  riskScore: number;
  executionComplexity: number;
}

@Injectable()
export class DynamicFlashloanStrategyService {
  private readonly logger = new Logger(DynamicFlashloanStrategyService.name);
  
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;

  // Strategy statistics
  private strategyStats: Map<string, StrategyStats> = new Map();
  private readonly PERFORMANCE_WINDOW = 100; // Track last 100 executions

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
    private readonly flashloanStrategy: FlashloanStrategyService,
    private readonly uniswapV3FlashSwapStrategy: UniswapV3FlashSwapStrategyService,
    private readonly flashbotsManager: FlashbotsBundleManagerService,
    private readonly flashbotsExecutor: FlashbotsExecutorService,
    private readonly eventEmitter: EventEmitter2,
    private readonly mevShareFlashloanStrategy?: MEVShareFlashloanStrategyService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
    this.wallet = new ethers.Wallet(this.configService.privateKey, this.provider);
    
    this.initializeStrategyStats();
  }

  private initializeStrategyStats(): void {
    const strategies = ['aave', 'balancer', 'uniswap-v3', 'mev-share'];
    
    strategies.forEach(strategy => {
      this.strategyStats.set(strategy, {
        totalOpportunities: 0,
        successfulExecutions: 0,
        totalProfit: 0n,
        averageGasCost: 0n,
        averageExecutionTime: 0,
        successRate: 0,
        profitability: 0
      });
    });
  }

  /**
   * Find the best flashloan opportunity across all strategies
   */
  async findBestFlashloanOpportunity(): Promise<EnhancedFlashloanRoute | null> {
    try {
      this.logger.debug('🔍 Scanning for best flashloan opportunity across all strategies');

      const opportunities: EnhancedFlashloanRoute[] = [];

      // Get opportunities from each strategy
      const aaveOpportunities = await this.getAaveOpportunities();
      const uniswapV3Opportunities = await this.getUniswapV3Opportunities();
      const mevShareOpportunities = await this.getMevShareOpportunities();

      opportunities.push(...aaveOpportunities, ...uniswapV3Opportunities, ...mevShareOpportunities);

      if (opportunities.length === 0) {
        return null;
      }

      // Select the best opportunity based on multiple criteria
      const bestOpportunity = this.selectBestOpportunity(opportunities);

      if (bestOpportunity) {
        this.logger.log('💎 Best flashloan opportunity selected', {
          strategy: bestOpportunity.strategy,
          netProfit: ethers.formatEther(bestOpportunity.netProfit),
          confidence: bestOpportunity.confidence,
          riskScore: bestOpportunity.riskScore
        });
      }

      return bestOpportunity;

    } catch (error) {
      this.logger.error('Error finding best flashloan opportunity:', error);
      return null;
    }
  }

  private async getAaveOpportunities(): Promise<EnhancedFlashloanRoute[]> {
    try {
      const routes = await this.flashloanStrategy.scanForFlashloanOpportunities();
      
      return routes.map(route => this.enhanceRoute(route, 'aave'));
    } catch (error) {
      this.logger.error('Error getting Aave opportunities:', error);
      return [];
    }
  }

  private async getUniswapV3Opportunities(): Promise<EnhancedFlashloanRoute[]> {
    try {
      const routes = await this.uniswapV3FlashSwapStrategy.scanForFlashSwapOpportunities();
      
      return routes.map(route => this.enhanceRoute(route, 'uniswap-v3'));
    } catch (error) {
      this.logger.error('Error getting Uniswap V3 opportunities:', error);
      return [];
    }
  }

  private async getMevShareOpportunities(): Promise<EnhancedFlashloanRoute[]> {
    try {
      if (!this.mevShareFlashloanStrategy || !this.configService.enableMevShare) {
        return [];
      }

      const routes = await this.mevShareFlashloanStrategy.scanForMevShareOpportunities();
      
      return routes.map(route => this.enhanceRoute(route, 'mev-share'));
    } catch (error) {
      this.logger.error('Error getting MEV-Share opportunities:', error);
      return [];
    }
  }

  private enhanceRoute(route: FlashloanRoute, strategy: string): EnhancedFlashloanRoute {
    // Estimate gas cost based on strategy
    const estimatedGasCost = this.estimateGasCost(strategy);
    
    // Calculate net profit
    const netProfit = route.expectedProfit > estimatedGasCost 
      ? route.expectedProfit - estimatedGasCost 
      : 0n;

    // Calculate risk score
    const riskScore = this.calculateRiskScore(route, strategy);
    
    // Calculate execution complexity
    const executionComplexity = this.calculateExecutionComplexity(route, strategy);

    return {
      ...route,
      strategy: strategy as any,
      estimatedGasCost,
      netProfit,
      riskScore,
      executionComplexity
    };
  }

  private estimateGasCost(strategy: string): bigint {
    // Estimate gas cost based on strategy type
    const gasEstimates = {
      'aave': 400000n,
      'balancer': 350000n,
      'uniswap-v3': 300000n,
      'mev-share': 450000n
    };

    const gasLimit = gasEstimates[strategy] || 400000n;
    const gasPrice = ethers.parseUnits('20', 'gwei'); // Simplified gas price
    
    return gasLimit * gasPrice;
  }

  private calculateRiskScore(route: FlashloanRoute, strategy: string): number {
    let riskScore = 50; // Base risk score

    // Strategy-specific risk adjustments
    const strategyRisk = {
      'aave': 10,        // Lower risk, established protocol
      'balancer': 15,    // Medium risk
      'uniswap-v3': 20,  // Higher risk due to complexity
      'mev-share': 30    // Highest risk, newer protocol
    };

    riskScore += strategyRisk[strategy] || 20;

    // Adjust based on confidence
    riskScore -= route.confidence * 0.3;

    // Adjust based on profit size (larger profits = higher risk)
    const profitInEth = Number(ethers.formatEther(route.expectedProfit));
    if (profitInEth > 1) {
      riskScore += 10;
    }

    return Math.max(0, Math.min(100, riskScore));
  }

  private calculateExecutionComplexity(route: FlashloanRoute, strategy: string): number {
    let complexity = 50; // Base complexity

    // Strategy-specific complexity
    const strategyComplexity = {
      'aave': 60,        // Medium complexity
      'balancer': 70,    // Higher complexity
      'uniswap-v3': 80,  // High complexity
      'mev-share': 90    // Highest complexity
    };

    complexity = strategyComplexity[strategy] || 60;

    // Adjust based on number of steps in arbitrage
    if (route.arbitrageRoute && route.arbitrageRoute.steps) {
      complexity += route.arbitrageRoute.steps.length * 5;
    }

    return Math.min(100, complexity);
  }

  private selectBestOpportunity(opportunities: EnhancedFlashloanRoute[]): EnhancedFlashloanRoute | null {
    if (opportunities.length === 0) {
      return null;
    }

    // Score each opportunity based on multiple factors
    const scoredOpportunities = opportunities.map(opportunity => ({
      opportunity,
      score: this.calculateOpportunityScore(opportunity)
    }));

    // Sort by score (highest first)
    scoredOpportunities.sort((a, b) => b.score - a.score);

    // Return the best opportunity
    return scoredOpportunities[0].opportunity;
  }

  private calculateOpportunityScore(opportunity: EnhancedFlashloanRoute): number {
    let score = 0;

    // Net profit weight (40%)
    const profitInEth = Number(ethers.formatEther(opportunity.netProfit));
    score += profitInEth * 40;

    // Confidence weight (25%)
    score += opportunity.confidence * 0.25;

    // Risk score weight (20%) - lower risk = higher score
    score += (100 - opportunity.riskScore) * 0.2;

    // Strategy performance weight (10%)
    const strategyStats = this.strategyStats.get(opportunity.strategy);
    if (strategyStats) {
      score += strategyStats.successRate * 0.1;
    }

    // Execution complexity weight (5%) - lower complexity = higher score
    score += (100 - opportunity.executionComplexity) * 0.05;

    return score;
  }

  async executeFlashloan(route: EnhancedFlashloanRoute): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      this.logger.log('⚡ Executing dynamic flashloan', {
        strategy: route.strategy,
        netProfit: ethers.formatEther(route.netProfit),
        riskScore: route.riskScore
      });

      const startTime = Date.now();
      let result;

      // Execute based on strategy
      switch (route.strategy) {
        case 'aave':
          result = await this.flashloanStrategy.executeFlashloan(route);
          break;
        case 'uniswap-v3':
          result = await this.uniswapV3FlashSwapStrategy.executeFlashSwap(route);
          break;
        case 'mev-share':
          if (this.mevShareFlashloanStrategy) {
            result = await this.mevShareFlashloanStrategy.executeFlashloan(route);
          } else {
            result = { success: false, error: 'MEV-Share strategy not available' };
          }
          break;
        default:
          result = { success: false, error: 'Unknown strategy' };
      }

      const executionTime = Date.now() - startTime;

      // Update strategy statistics
      this.updateStrategyStats(route.strategy, result.success, route.netProfit, route.estimatedGasCost, executionTime);

      if (result.success) {
        this.logger.log('✅ Dynamic flashloan executed successfully', {
          strategy: route.strategy,
          txHash: result.txHash,
          executionTime: `${executionTime}ms`
        });
      }

      return result;

    } catch (error) {
      this.logger.error('❌ Dynamic flashloan execution failed:', error);
      this.updateStrategyStats(route.strategy, false, 0n, route.estimatedGasCost, 0);
      return { success: false, error: error.message };
    }
  }

  private updateStrategyStats(
    strategy: string,
    success: boolean,
    profit: bigint,
    gasCost: bigint,
    executionTime: number
  ): void {
    const stats = this.strategyStats.get(strategy);
    if (!stats) return;

    stats.totalOpportunities++;
    if (success) {
      stats.successfulExecutions++;
      stats.totalProfit += profit;
    }

    // Update averages
    stats.averageGasCost = (stats.averageGasCost + gasCost) / 2n;
    stats.averageExecutionTime = (stats.averageExecutionTime + executionTime) / 2;
    stats.successRate = (stats.successfulExecutions / stats.totalOpportunities) * 100;
    stats.profitability = Number(ethers.formatEther(stats.totalProfit)) / stats.totalOpportunities;

    this.strategyStats.set(strategy, stats);
  }

  setMEVShareStrategy(strategy: MEVShareFlashloanStrategyService): void {
    // Allow setting MEV-Share strategy dynamically
    // This is used when MEV-Share is enabled
  }

  getStrategyStats(): Map<string, StrategyStats> {
    return new Map(this.strategyStats);
  }
}
