import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';

export interface GasEstimate {
  slow: bigint;
  standard: bigint;
  fast: bigint;
  instant: bigint;
  baseFee?: bigint;
  priorityFee?: bigint;
  source: string;
  timestamp: number;
}

export interface GasEstimationConfig {
  blocknativeApiKey?: string;
  enableBlocknative: boolean;
  enable0xApi: boolean;
  enableEthGasStation: boolean;
  fallbackGasPrice: bigint;
}

@Injectable()
export class AdvancedGasEstimatorService {
  private readonly logger = new Logger(AdvancedGasEstimatorService.name);
  
  private provider: ethers.JsonRpcProvider;
  private config: GasEstimationConfig;
  private cache: Map<string, { estimate: GasEstimate; expiry: number }> = new Map();
  private readonly CACHE_DURATION = 15000; // 15 seconds

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
    this.config = {
      blocknativeApiKey: this.configService.get('blocknativeApiKey'),
      enableBlocknative: this.configService.get('enableBlocknativeGas', false),
      enable0xApi: this.configService.get('enable0xApiGas', true),
      enableEthGasStation: this.configService.get('enableEthGasStation', true),
      fallbackGasPrice: ethers.parseUnits(this.configService.get('fallbackGasPriceGwei', 20).toString(), 'gwei'),
    };
  }

  /**
   * Get comprehensive gas estimates from multiple sources
   */
  async getGasEstimates(): Promise<GasEstimate> {
    try {
      // Check cache first
      const cached = this.getFromCache('gas-estimates');
      if (cached) {
        return cached;
      }

      const estimates: GasEstimate[] = [];

      // Try multiple sources in parallel
      const promises = [];

      if (this.config.enableBlocknative && this.config.blocknativeApiKey) {
        promises.push(this.getBlocknativeEstimate());
      }

      if (this.config.enable0xApi) {
        promises.push(this.get0xApiEstimate());
      }

      if (this.config.enableEthGasStation) {
        promises.push(this.getEthGasStationEstimate());
      }

      // Always include provider estimate as fallback
      promises.push(this.getProviderEstimate());

      const results = await Promise.allSettled(promises);
      
      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          estimates.push(result.value);
        }
      });

      if (estimates.length === 0) {
        throw new Error('No gas estimates available');
      }

      // Combine estimates using weighted average
      const combinedEstimate = this.combineEstimates(estimates);
      
      // Cache the result
      this.setCache('gas-estimates', combinedEstimate);
      
      return combinedEstimate;

    } catch (error) {
      this.logger.error('Error getting gas estimates:', error);
      return this.getFallbackEstimate();
    }
  }

  private async getBlocknativeEstimate(): Promise<GasEstimate | null> {
    try {
      const response = await axios.get('https://api.blocknative.com/gasprices/blockprices', {
        headers: {
          'Authorization': this.config.blocknativeApiKey
        },
        timeout: 5000
      });

      const data = response.data;
      if (!data.blockPrices || data.blockPrices.length === 0) {
        return null;
      }

      const prices = data.blockPrices[0].estimatedPrices;
      
      return {
        slow: ethers.parseUnits(prices.find(p => p.confidence === 70)?.price || '20', 'gwei'),
        standard: ethers.parseUnits(prices.find(p => p.confidence === 80)?.price || '25', 'gwei'),
        fast: ethers.parseUnits(prices.find(p => p.confidence === 90)?.price || '30', 'gwei'),
        instant: ethers.parseUnits(prices.find(p => p.confidence === 95)?.price || '35', 'gwei'),
        baseFee: ethers.parseUnits(data.blockPrices[0].baseFeePerGas || '20', 'gwei'),
        source: 'blocknative',
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.debug('Blocknative gas estimate failed:', error);
      return null;
    }
  }

  private async get0xApiEstimate(): Promise<GasEstimate | null> {
    try {
      const response = await axios.get('https://api.0x.org/gasinfo', {
        timeout: 5000
      });

      const data = response.data;
      
      return {
        slow: ethers.parseUnits(data.slow || '20', 'gwei'),
        standard: ethers.parseUnits(data.standard || '25', 'gwei'),
        fast: ethers.parseUnits(data.fast || '30', 'gwei'),
        instant: ethers.parseUnits(data.instant || '35', 'gwei'),
        source: '0x-api',
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.debug('0x API gas estimate failed:', error);
      return null;
    }
  }

  private async getEthGasStationEstimate(): Promise<GasEstimate | null> {
    try {
      const response = await axios.get('https://ethgasstation.info/api/ethgasAPI.json', {
        timeout: 5000
      });

      const data = response.data;
      
      return {
        slow: ethers.parseUnits((data.safeLow / 10).toString(), 'gwei'),
        standard: ethers.parseUnits((data.standard / 10).toString(), 'gwei'),
        fast: ethers.parseUnits((data.fast / 10).toString(), 'gwei'),
        instant: ethers.parseUnits((data.fastest / 10).toString(), 'gwei'),
        source: 'ethgasstation',
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.debug('ETH Gas Station estimate failed:', error);
      return null;
    }
  }

  private async getProviderEstimate(): Promise<GasEstimate> {
    try {
      const feeData = await this.provider.getFeeData();
      const gasPrice = feeData.gasPrice || this.config.fallbackGasPrice;
      
      return {
        slow: gasPrice,
        standard: gasPrice * 12n / 10n, // 1.2x
        fast: gasPrice * 15n / 10n, // 1.5x
        instant: gasPrice * 2n, // 2x
        baseFee: feeData.maxFeePerGas || gasPrice,
        priorityFee: feeData.maxPriorityFeePerGas || ethers.parseUnits('2', 'gwei'),
        source: 'provider',
        timestamp: Date.now()
      };

    } catch (error) {
      this.logger.error('Provider gas estimate failed:', error);
      return this.getFallbackEstimate();
    }
  }

  private combineEstimates(estimates: GasEstimate[]): GasEstimate {
    if (estimates.length === 1) {
      return estimates[0];
    }

    // Weight estimates by source reliability
    const weights = {
      'blocknative': 0.4,
      '0x-api': 0.3,
      'ethgasstation': 0.2,
      'provider': 0.1
    };

    let totalWeight = 0;
    let weightedSlow = 0n;
    let weightedStandard = 0n;
    let weightedFast = 0n;
    let weightedInstant = 0n;

    estimates.forEach(estimate => {
      const weight = weights[estimate.source] || 0.1;
      totalWeight += weight;
      
      weightedSlow += estimate.slow * BigInt(Math.floor(weight * 100)) / 100n;
      weightedStandard += estimate.standard * BigInt(Math.floor(weight * 100)) / 100n;
      weightedFast += estimate.fast * BigInt(Math.floor(weight * 100)) / 100n;
      weightedInstant += estimate.instant * BigInt(Math.floor(weight * 100)) / 100n;
    });

    const totalWeightBigInt = BigInt(Math.floor(totalWeight * 100));

    return {
      slow: weightedSlow * 100n / totalWeightBigInt,
      standard: weightedStandard * 100n / totalWeightBigInt,
      fast: weightedFast * 100n / totalWeightBigInt,
      instant: weightedInstant * 100n / totalWeightBigInt,
      baseFee: estimates.find(e => e.baseFee)?.baseFee,
      priorityFee: estimates.find(e => e.priorityFee)?.priorityFee,
      source: 'combined',
      timestamp: Date.now()
    };
  }

  private getFallbackEstimate(): GasEstimate {
    const fallback = this.config.fallbackGasPrice;
    
    return {
      slow: fallback,
      standard: fallback * 12n / 10n,
      fast: fallback * 15n / 10n,
      instant: fallback * 2n,
      source: 'fallback',
      timestamp: Date.now()
    };
  }

  private getFromCache(key: string): GasEstimate | null {
    const cached = this.cache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached.estimate;
    }
    return null;
  }

  private setCache(key: string, estimate: GasEstimate): void {
    this.cache.set(key, {
      estimate,
      expiry: Date.now() + this.CACHE_DURATION
    });
  }

  async estimateGasForTransaction(transaction: any): Promise<bigint> {
    try {
      return await this.provider.estimateGas(transaction);
    } catch (error) {
      this.logger.error('Error estimating gas for transaction:', error);
      return 200000n; // Fallback gas limit
    }
  }

  clearCache(): void {
    this.cache.clear();
    this.logger.log('Gas estimation cache cleared');
  }
}
