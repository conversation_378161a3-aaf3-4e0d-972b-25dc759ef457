import { Module } from '@nestjs/common';
import { FlashbotsExecutorService } from './flashbots-executor.service';
import { PriceCalculatorService } from './price-calculator.service';
import { SwapDataBuilderService } from './swap-data-builder.service';

@Module({
  providers: [
    FlashbotsExecutorService,
    PriceCalculatorService,
    SwapDataBuilderService,
  ],
  exports: [
    FlashbotsExecutorService,
    PriceCalculatorService,
    SwapDataBuilderService,
  ],
})
export class ExecutionModule {}
