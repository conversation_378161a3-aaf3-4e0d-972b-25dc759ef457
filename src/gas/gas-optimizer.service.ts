import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';
import { AdvancedGasEstimatorService } from './advanced-gas-estimator.service';
import { GasStrategy } from '../types';

@Injectable()
export class GasOptimizerService {
  private readonly logger = new Logger(GasOptimizerService.name);
  
  private provider: ethers.JsonRpcProvider;
  private gasHistory: Array<{ timestamp: number; baseFee: ethers.BigNumberish; priorityFee: ethers.BigNumberish }> = [];
  private readonly HISTORY_SIZE = 100;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
    private readonly advancedEstimator: AdvancedGasEstimatorService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
  }

  async getCurrentGasStrategy(): Promise<GasStrategy> {
    try {
      // Get advanced gas estimates from multiple sources
      const advancedEstimates = await this.advancedEstimator.getGasEstimates();

      // Get provider data as fallback
      const [feeData, block] = await Promise.all([
        this.provider.getFeeData(),
        this.provider.getBlock('latest')
      ]);

      if (!feeData.gasPrice || !block) {
        throw new Error('Failed to fetch gas data');
      }

      // Use advanced estimates if available, otherwise fallback to provider
      const baseFee = advancedEstimates.baseFee || feeData.maxFeePerGas || feeData.gasPrice;
      const priorityFee = advancedEstimates.priorityFee || feeData.maxPriorityFeePerGas || ethers.parseUnits('2', 'gwei');

      // Store in history
      this.addToHistory(baseFee, priorityFee);

      // Calculate optimized gas strategy using advanced estimates
      const strategy = this.calculateOptimalStrategyAdvanced(advancedEstimates, baseFee, priorityFee);

      return strategy;
    } catch (error) {
      this.logger.error('Error getting current gas strategy:', error);
      return this.getFallbackStrategy();
    }
  }

  private addToHistory(baseFee: ethers.BigNumberish, priorityFee: ethers.BigNumberish): void {
    this.gasHistory.push({
      timestamp: Date.now(),
      baseFee,
      priorityFee
    });

    // Keep only recent history
    if (this.gasHistory.length > this.HISTORY_SIZE) {
      this.gasHistory = this.gasHistory.slice(-this.HISTORY_SIZE);
    }
  }

  private calculateOptimalStrategyAdvanced(
    advancedEstimates: any,
    baseFee: ethers.BigNumberish,
    priorityFee: ethers.BigNumberish
  ): GasStrategy {
    const baseFeeBigInt = BigInt(baseFee.toString());
    const priorityFeeBigInt = BigInt(priorityFee.toString());

    // Calculate different speed strategies
    const strategies = {
      slow: {
        maxFeePerGas: baseFeeBigInt + priorityFeeBigInt,
        maxPriorityFeePerGas: priorityFeeBigInt,
        gasLimit: 200000n
      },
      standard: {
        maxFeePerGas: baseFeeBigInt + (priorityFeeBigInt * 12n / 10n), // 1.2x priority fee
        maxPriorityFeePerGas: priorityFeeBigInt * 12n / 10n,
        gasLimit: 250000n
      },
      fast: {
        maxFeePerGas: baseFeeBigInt + (priorityFeeBigInt * 15n / 10n), // 1.5x priority fee
        maxPriorityFeePerGas: priorityFeeBigInt * 15n / 10n,
        gasLimit: 300000n
      },
      urgent: {
        maxFeePerGas: baseFeeBigInt + (priorityFeeBigInt * 2n), // 2x priority fee
        maxPriorityFeePerGas: priorityFeeBigInt * 2n,
        gasLimit: 400000n
      }
    };

    // Apply configuration limits
    const maxGasPriceWei = ethers.parseUnits(this.configService.maxGasPriceGwei.toString(), 'gwei');
    const maxPriorityFeeWei = ethers.parseUnits(this.configService.maxPriorityFeeGwei.toString(), 'gwei');

    // Select strategy based on network conditions and configuration
    let selectedStrategy = strategies.standard;

    // Use urgent strategy for high-value opportunities
    if (advancedEstimates.confidence > 80) {
      selectedStrategy = strategies.urgent;
    } else if (advancedEstimates.confidence > 60) {
      selectedStrategy = strategies.fast;
    }

    // Apply limits
    selectedStrategy.maxFeePerGas = selectedStrategy.maxFeePerGas > maxGasPriceWei 
      ? maxGasPriceWei 
      : selectedStrategy.maxFeePerGas;
    
    selectedStrategy.maxPriorityFeePerGas = selectedStrategy.maxPriorityFeePerGas > maxPriorityFeeWei 
      ? maxPriorityFeeWei 
      : selectedStrategy.maxPriorityFeePerGas;

    return {
      maxFeePerGas: selectedStrategy.maxFeePerGas,
      maxPriorityFeePerGas: selectedStrategy.maxPriorityFeePerGas,
      gasLimit: selectedStrategy.gasLimit,
      type: 2 // EIP-1559
    };
  }

  private getFallbackStrategy(): GasStrategy {
    const fallbackGasPrice = ethers.parseUnits(this.configService.get('fallbackGasPriceGwei', 20).toString(), 'gwei');
    
    return {
      maxFeePerGas: fallbackGasPrice,
      maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei'),
      gasLimit: 250000n,
      type: 2
    };
  }

  async optimizeForMEV(expectedProfit: bigint, urgency: 'low' | 'medium' | 'high' = 'medium'): Promise<GasStrategy> {
    try {
      const baseStrategy = await this.getCurrentGasStrategy();
      
      // Calculate maximum gas cost we can afford (percentage of expected profit)
      const maxGasCostRatio = urgency === 'high' ? 0.3 : urgency === 'medium' ? 0.2 : 0.1;
      const maxGasCost = expectedProfit * BigInt(Math.floor(maxGasCostRatio * 100)) / 100n;
      
      // Calculate maximum gas price we can afford
      const maxAffordableGasPrice = maxGasCost / baseStrategy.gasLimit;
      
      // Use the lower of our calculated max and the base strategy
      const optimizedMaxFee = baseStrategy.maxFeePerGas < maxAffordableGasPrice 
        ? baseStrategy.maxFeePerGas 
        : maxAffordableGasPrice;
      
      const optimizedPriorityFee = baseStrategy.maxPriorityFeePerGas < maxAffordableGasPrice 
        ? baseStrategy.maxPriorityFeePerGas 
        : maxAffordableGasPrice / 2n;

      return {
        maxFeePerGas: optimizedMaxFee,
        maxPriorityFeePerGas: optimizedPriorityFee,
        gasLimit: baseStrategy.gasLimit,
        type: 2
      };

    } catch (error) {
      this.logger.error('Error optimizing gas for MEV:', error);
      return this.getFallbackStrategy();
    }
  }

  async estimateTransactionCost(gasLimit: bigint, strategy?: GasStrategy): Promise<bigint> {
    try {
      const gasStrategy = strategy || await this.getCurrentGasStrategy();
      return gasLimit * gasStrategy.maxFeePerGas;
    } catch (error) {
      this.logger.error('Error estimating transaction cost:', error);
      const fallback = this.getFallbackStrategy();
      return gasLimit * fallback.maxFeePerGas;
    }
  }

  getGasHistory(): Array<{ timestamp: number; baseFee: ethers.BigNumberish; priorityFee: ethers.BigNumberish }> {
    return [...this.gasHistory];
  }

  async getOptimalGasForBundle(transactions: number, totalValue: bigint): Promise<GasStrategy> {
    try {
      const baseStrategy = await this.getCurrentGasStrategy();
      
      // Increase gas limit for bundle transactions
      const bundleGasLimit = baseStrategy.gasLimit * BigInt(transactions) + 50000n; // Extra for bundle overhead
      
      // Adjust gas price based on bundle value
      const valueBasedMultiplier = totalValue > ethers.parseEther('10') ? 15n : 12n; // 1.5x or 1.2x
      
      return {
        maxFeePerGas: baseStrategy.maxFeePerGas * valueBasedMultiplier / 10n,
        maxPriorityFeePerGas: baseStrategy.maxPriorityFeePerGas * valueBasedMultiplier / 10n,
        gasLimit: bundleGasLimit,
        type: 2
      };

    } catch (error) {
      this.logger.error('Error getting optimal gas for bundle:', error);
      return this.getFallbackStrategy();
    }
  }

  async predictGasTrend(minutes: number = 5): Promise<'rising' | 'falling' | 'stable'> {
    try {
      if (this.gasHistory.length < 10) {
        return 'stable';
      }

      const cutoffTime = Date.now() - (minutes * 60 * 1000);
      const recentHistory = this.gasHistory.filter(h => h.timestamp > cutoffTime);
      
      if (recentHistory.length < 5) {
        return 'stable';
      }

      // Calculate trend based on base fee changes
      const firstBaseFee = Number(recentHistory[0].baseFee.toString());
      const lastBaseFee = Number(recentHistory[recentHistory.length - 1].baseFee.toString());
      
      const changePercent = ((lastBaseFee - firstBaseFee) / firstBaseFee) * 100;
      
      if (changePercent > 10) {
        return 'rising';
      } else if (changePercent < -10) {
        return 'falling';
      } else {
        return 'stable';
      }

    } catch (error) {
      this.logger.error('Error predicting gas trend:', error);
      return 'stable';
    }
  }

  clearHistory(): void {
    this.gasHistory = [];
    this.logger.log('Gas history cleared');
  }
}
