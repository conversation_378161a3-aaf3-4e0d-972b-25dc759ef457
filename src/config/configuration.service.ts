import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TokenConfig, DexConfig, DexName, TokenSymbol, TokenCategory } from '../types';

@Injectable()
export class ConfigurationService {
  constructor(private configService: ConfigService) {}

  // Network configuration
  get chainId(): number {
    return this.configService.get<number>('app.chainId', 11155111);
  }

  get rpcUrl(): string {
    return this.configService.get<string>('app.rpcUrl', 'http://localhost:8545');
  }

  get privateKey(): string {
    return this.configService.get<string>('app.privateKey', '');
  }

  get flashbotsSignerKey(): string {
    return this.configService.get<string>('app.flashbotsSignerKey', '');
  }

  // MEV configuration
  get minProfitWei(): string {
    return this.configService.get<string>('app.minProfitWei', '100000000000000');
  }

  get maxGasPriceGwei(): number {
    return this.configService.get<number>('app.maxGasPriceGwei', 100);
  }

  get maxPriorityFeeGwei(): number {
    return this.configService.get<number>('app.maxPriorityFeeGwei', 5);
  }

  get slippageTolerance(): number {
    return this.configService.get<number>('app.slippageTolerance', 0.005);
  }

  // Strategy configuration
  get enableSandwichAttacks(): boolean {
    return this.configService.get<boolean>('app.enableSandwichAttacks', false);
  }

  get enableFrontRunning(): boolean {
    return this.configService.get<boolean>('app.enableFrontRunning', false);
  }

  get enableArbitrage(): boolean {
    return this.configService.get<boolean>('app.enableArbitrage', false);
  }

  get enableFlashloanAttacks(): boolean {
    return this.configService.get<boolean>('app.enableFlashloanAttacks', true);
  }

  get enableMultiBlockAttacks(): boolean {
    return this.configService.get<boolean>('app.enableMultiBlockAttacks', false);
  }

  // Flashloan configuration
  get flashloanTokens(): string[] {
    return this.configService.get<string[]>('app.flashloanTokens', ['USDC', 'WETH', 'USDT', 'DAI']);
  }

  get flashloanPrimaryToken(): string {
    return this.configService.get<string>('app.flashloanPrimaryToken', 'USDC');
  }

  get flashloanTargetTokens(): string[] {
    return this.configService.get<string[]>('app.flashloanTargetTokens', ['WETH', 'USDT', 'DAI']);
  }

  get enableAllTokenPairs(): boolean {
    return this.configService.get<boolean>('app.enableAllTokenPairs', true);
  }

  get uniswapV3TradingPairs(): string[] {
    return this.configService.get<string[]>('app.uniswapV3TradingPairs', ['WETH/USDC', 'WETH/USDT', 'WBTC/WETH', 'DAI/USDC']);
  }

  get uniswapV3FeeTiers(): number[] {
    return this.configService.get<number[]>('app.uniswapV3FeeTiers', [500, 3000, 10000]);
  }

  // Contract addresses
  get hybridFlashloanContract(): string {
    return this.configService.get<string>('app.hybridFlashloanContract', '');
  }

  get balancerFlashloanContract(): string {
    return this.configService.get<string>('app.balancerFlashloanContract', '');
  }

  get aaveFlashloanContract(): string {
    return this.configService.get<string>('app.aaveFlashloanContract', '');
  }

  // Flashbots configuration
  get enableFlashbots(): boolean {
    return this.configService.get<boolean>('app.enableFlashbots', false);
  }

  get flashbotsRelayUrl(): string {
    return this.configService.get<string>('app.flashbotsRelayUrl', 'https://relay.flashbots.net');
  }

  get flashbotsAuthKey(): string {
    return this.configService.get<string>('app.flashbotsAuthKey', '');
  }

  // MEV-Share configuration
  get enableMevShare(): boolean {
    return this.configService.get<boolean>('app.enableMevShare', false);
  }

  get mevShareStreamUrl(): string {
    return this.configService.get<string>('app.mevShareStreamUrl', 'https://mev-share.flashbots.net');
  }

  // Risk management
  get maxPositionSizeEth(): number {
    return this.configService.get<number>('app.maxPositionSizeEth', 10);
  }

  get emergencyStop(): boolean {
    return this.configService.get<boolean>('app.emergencyStop', false);
  }

  get dryRun(): boolean {
    return this.configService.get<boolean>('app.dryRun', false);
  }

  // Logging
  get logLevel(): string {
    return this.configService.get<string>('app.logLevel', 'info');
  }

  get logToFile(): boolean {
    return this.configService.get<boolean>('app.logToFile', true);
  }

  // Helper methods for backward compatibility
  getAllConfig() {
    return this.configService.get('app');
  }

  get(key: string, defaultValue?: any) {
    return this.configService.get(`app.${key}`, defaultValue);
  }
}
