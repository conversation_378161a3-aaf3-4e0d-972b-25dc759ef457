import { Modu<PERSON> } from '@nestjs/common';
import { SandwichStrategyService } from './sandwich-strategy.service';
import { ArbitrageStrategyService } from './arbitrage-strategy.service';
import { FlashloanStrategyService } from './flashloan-strategy.service';
import { MEVShareFlashloanStrategyService } from './mev-share-flashloan-strategy.service';
import { UniswapV3FlashSwapStrategyService } from './uniswap-v3-flash-strategy.service';
import { DynamicFlashloanStrategyService } from './dynamic-flashloan-strategy.service';
import { FrontrunningStrategyService } from './frontrunning-strategy.service';
import { MultiBlockStrategyService } from './multi-block-strategy.service';

@Module({
  providers: [
    SandwichStrategyService,
    ArbitrageStrategyService,
    FlashloanStrategyService,
    MEVShareFlashloanStrategyService,
    UniswapV3FlashSwapStrategyService,
    DynamicFlashloanStrategyService,
    FrontrunningStrategyService,
    MultiBlockStrategyService,
  ],
  exports: [
    SandwichStrategyService,
    ArbitrageStrategyService,
    FlashloanStrategyService,
    MEVShareFlashloanStrategyService,
    UniswapV3FlashSwapStrategyService,
    DynamicFlashloanStrategyService,
    FrontrunningStrategyService,
    MultiBlockStrategyService,
  ],
})
export class StrategiesModule {}
