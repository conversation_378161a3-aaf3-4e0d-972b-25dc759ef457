import { Injectable } from '@nestjs/common';
import winston from 'winston';
import { ConfigurationService } from '../config/configuration.service';
import { LogLevel, LogEntry } from '../types';

@Injectable()
export class LoggerService {
  private winston: winston.Logger;
  private logEntries: LogEntry[] = [];

  constructor(private readonly configService: ConfigurationService) {
    this.initializeLogger();
  }

  private initializeLogger(): void {
    const transports: winston.transport[] = [];

    // Only add console transport if split screen dashboard is not active
    if (!process.env.SPLIT_SCREEN_DASHBOARD) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.timestamp(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
              const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
              return `${timestamp} [${level}]: ${message} ${metaStr}`;
            })
          )
        })
      );
    }

    if (this.configService.logToFile) {
      transports.push(
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      );
    }

    this.winston = winston.createLogger({
      level: this.configService.logLevel,
      transports,
      exitOnError: false
    });
  }

  private addToBuffer(level: LogLevel, message: string, meta?: any): void {
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      meta
    };

    this.logEntries.push(entry);

    // Keep only last 1000 entries
    if (this.logEntries.length > 1000) {
      this.logEntries = this.logEntries.slice(-1000);
    }
  }

  error(message: string, meta?: any): void {
    this.winston.error(message, meta);
    this.addToBuffer('error', message, meta);
  }

  warn(message: string, meta?: any): void {
    this.winston.warn(message, meta);
    this.addToBuffer('warn', message, meta);
  }

  info(message: string, meta?: any): void {
    this.winston.info(message, meta);
    this.addToBuffer('info', message, meta);
  }

  debug(message: string, meta?: any): void {
    this.winston.debug(message, meta);
    this.addToBuffer('debug', message, meta);
  }

  verbose(message: string, meta?: any): void {
    this.winston.verbose(message, meta);
    this.addToBuffer('verbose', message, meta);
  }

  log(level: LogLevel, message: string, meta?: any): void {
    this.winston.log(level, message, meta);
    this.addToBuffer(level, message, meta);
  }

  // MEV-specific logging methods
  opportunity(message: string, data: any): void {
    this.info(`🎯 OPPORTUNITY: ${message}`, data);
  }

  execution(message: string, data: any): void {
    this.info(`⚡ EXECUTION: ${message}`, data);
  }

  profit(message: string, data: any): void {
    this.info(`💰 PROFIT: ${message}`, data);
  }

  gas(message: string, data: any): void {
    this.debug(`⛽ GAS: ${message}`, data);
  }

  mempool(message: string, data: any): void {
    this.debug(`🔍 MEMPOOL: ${message}`, data);
  }

  flashbots(message: string, data: any): void {
    this.info(`🚀 FLASHBOTS: ${message}`, data);
  }

  arbitrage(message: string, data: any): void {
    this.info(`🔄 ARBITRAGE: ${message}`, data);
  }

  sandwich(message: string, data: any): void {
    this.info(`🥪 SANDWICH: ${message}`, data);
  }

  frontrun(message: string, data: any): void {
    this.info(`🏃 FRONTRUN: ${message}`, data);
  }

  flashloan(message: string, data: any): void {
    this.info(`⚡ FLASHLOAN: ${message}`, data);
  }

  // Performance logging
  performance(operation: string, duration: number, data?: any): void {
    this.debug(`📊 PERFORMANCE: ${operation} took ${duration}ms`, data);
  }

  // System status logging
  systemStatus(message: string, data?: any): void {
    this.info(`🔧 SYSTEM: ${message}`, data);
  }

  // Network logging
  network(message: string, data?: any): void {
    this.debug(`🌐 NETWORK: ${message}`, data);
  }

  // Security logging
  security(message: string, data?: any): void {
    this.warn(`🔒 SECURITY: ${message}`, data);
  }

  // Get recent log entries
  getRecentLogs(count: number = 100): LogEntry[] {
    return this.logEntries.slice(-count);
  }

  // Get logs by level
  getLogsByLevel(level: LogLevel, count: number = 100): LogEntry[] {
    return this.logEntries
      .filter(entry => entry.level === level)
      .slice(-count);
  }

  // Get logs in time range
  getLogsInRange(startTime: Date, endTime: Date): LogEntry[] {
    return this.logEntries.filter(entry => 
      entry.timestamp >= startTime && entry.timestamp <= endTime
    );
  }

  // Clear log buffer
  clearBuffer(): void {
    this.logEntries = [];
  }

  // Get log statistics
  getLogStats(): { [key in LogLevel]: number } {
    const stats = {
      error: 0,
      warn: 0,
      info: 0,
      debug: 0,
      verbose: 0
    };

    this.logEntries.forEach(entry => {
      stats[entry.level]++;
    });

    return stats;
  }

  // Export logs to file
  async exportLogs(filename: string): Promise<void> {
    const fs = await import('fs/promises');
    const data = JSON.stringify(this.logEntries, null, 2);
    await fs.writeFile(filename, data);
  }

  // Set log level dynamically
  setLogLevel(level: LogLevel): void {
    this.winston.level = level;
  }

  // Get current log level
  getLogLevel(): string {
    return this.winston.level;
  }
}
