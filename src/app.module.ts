import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Core modules
import { CoreModule } from './core/core.module';
import { ConfigurationModule } from './config/config.module';
import { ProvidersModule } from './providers/providers.module';
import { StrategiesModule } from './strategies/strategies.module';
import { UtilsModule } from './utils/utils.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { ExecutionModule } from './execution/execution.module';
import { SimulationModule } from './simulation/simulation.module';
import { FlashbotsModule } from './flashbots/flashbots.module';
import { GasModule } from './gas/gas.module';
import { DexModule } from './dex/dex.module';
import { ContractsModule } from './contracts/contracts.module';
import { WorkersModule } from './workers/workers.module';

// Configuration
import { configValidationSchema } from './config/config.validation';

@Module({
  imports: [
    // NestJS core modules
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env', '.env.local', '.env.sepolia', '.env.mainnet'],
      validationSchema: configValidationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
    }),
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // Application modules
    ConfigurationModule,
    ProvidersModule,
    UtilsModule,
    MonitoringModule,
    DexModule,
    ContractsModule,
    GasModule,
    SimulationModule,
    FlashbotsModule,
    ExecutionModule,
    StrategiesModule,
    WorkersModule,
    CoreModule, // Core module should be last as it depends on others
  ],
})
export class AppModule {}
