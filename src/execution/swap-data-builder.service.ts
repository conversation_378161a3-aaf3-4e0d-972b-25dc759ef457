import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';

@Injectable()
export class SwapDataBuilderService {
  private readonly logger = new Logger(SwapDataBuilderService.name);

  buildUniswapV2SwapData(
    tokenIn: string,
    tokenOut: string,
    amountIn: bigint,
    amountOutMin: bigint,
    to: string,
    deadline: number
  ): string {
    // Uniswap V2 Router swapExactTokensForTokens function
    const iface = new ethers.Interface([
      'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)'
    ]);

    return iface.encodeFunctionData('swapExactTokensForTokens', [
      amountIn,
      amountOutMin,
      [tokenIn, tokenOut],
      to,
      deadline
    ]);
  }

  buildUniswapV3SwapData(
    tokenIn: string,
    tokenOut: string,
    fee: number,
    amountIn: bigint,
    amountOutMin: bigint,
    to: string,
    deadline: number
  ): string {
    // Uniswap V3 Router exactInputSingle function
    const iface = new ethers.Interface([
      'function exactInputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum, uint160 sqrtPriceLimitX96)) external returns (uint256 amountOut)'
    ]);

    const params = {
      tokenIn,
      tokenOut,
      fee,
      recipient: to,
      deadline,
      amountIn,
      amountOutMinimum: amountOutMin,
      sqrtPriceLimitX96: 0 // No price limit
    };

    return iface.encodeFunctionData('exactInputSingle', [params]);
  }

  buildFlashloanCalldata(
    asset: string,
    amount: bigint,
    params: string
  ): string {
    // Aave V3 flashloan function
    const iface = new ethers.Interface([
      'function flashLoan(address receiverAddress, address[] calldata assets, uint256[] calldata amounts, uint256[] calldata modes, address onBehalfOf, bytes calldata params, uint16 referralCode) external'
    ]);

    return iface.encodeFunctionData('flashLoan', [
      ethers.ZeroAddress, // receiverAddress (would be actual contract)
      [asset],
      [amount],
      [0], // mode 0 = no debt
      ethers.ZeroAddress,
      params,
      0 // referralCode
    ]);
  }
}
