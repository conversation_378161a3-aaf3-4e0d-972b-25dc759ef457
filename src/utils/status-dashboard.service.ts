import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import chalk from 'chalk';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';

export interface SuccessfulTransaction {
  timestamp: number;
  type: 'flashloan' | 'arbitrage' | 'sandwich' | 'mev-share' | 'uniswap-v3-flash' | 'dynamic-flashloan';
  profit: bigint;
  gasUsed: bigint;
  txHash?: string;
  bundleHash?: string;
  confidence: number;
  details?: string;
}

export interface BotConfiguration {
  // Token configuration
  primaryToken: string;
  targetTokens: string[];
  enableAllTokenPairs: boolean;

  // DEX configuration
  flashloanDexPairs: string[];
  buyDex: string;
  sellDex: string;
  enableCrossDex: boolean;

  // Profit thresholds
  minProfitWei: string;
  minArbitrageSpread: string;
  minBackrunProfitEth: string;

  // Risk management
  maxPositionSizeEth: string;
  maxGasCostEth: string;
  slippageTolerance: string;

  // Scanning intervals
  arbitrageScanInterval: string;
  flashloanScanInterval: string;

  // Network settings
  chainId: number;
  dryRun: boolean;
}

export interface DashboardStats {
  // Mempool stats
  totalTransactions: number;
  relevantTransactions: number;
  opportunitiesFound: number;
  successfulExecutions: number;

  // Financial stats
  totalProfit: bigint;
  totalGasSpent: bigint;
  netProfit: bigint;
  averageProfit: bigint;

  // Performance stats
  successRate: number;
  averageExecutionTime: number;
  uptime: number;

  // Strategy stats
  flashloanCount: number;
  arbitrageCount: number;
  sandwichCount: number;
  frontrunCount: number;
}

@Injectable()
export class StatusDashboardService {
  private readonly logger = new Logger(StatusDashboardService.name);
  
  private stats: DashboardStats;
  private successfulTransactions: SuccessfulTransaction[] = [];
  private startTime: number;
  private isActive = false;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.startTime = Date.now();
    this.initializeStats();
    this.setupEventListeners();
  }

  private initializeStats(): void {
    this.stats = {
      totalTransactions: 0,
      relevantTransactions: 0,
      opportunitiesFound: 0,
      successfulExecutions: 0,
      totalProfit: 0n,
      totalGasSpent: 0n,
      netProfit: 0n,
      averageProfit: 0n,
      successRate: 0,
      averageExecutionTime: 0,
      uptime: 0,
      flashloanCount: 0,
      arbitrageCount: 0,
      sandwichCount: 0,
      frontrunCount: 0
    };
  }

  private setupEventListeners(): void {
    // Listen for mempool transactions
    this.eventEmitter.on('mempool.pendingTransaction', () => {
      this.stats.totalTransactions++;
    });

    this.eventEmitter.on('mempool.relevantTransaction', () => {
      this.stats.relevantTransactions++;
    });

    // Listen for opportunities
    this.eventEmitter.on('opportunity.found', () => {
      this.stats.opportunitiesFound++;
    });

    // Listen for successful executions
    this.eventEmitter.on('execution.success', (data: SuccessfulTransaction) => {
      this.recordSuccessfulTransaction(data);
    });

    // Listen for gas usage
    this.eventEmitter.on('gas.spent', (amount: bigint) => {
      this.stats.totalGasSpent += amount;
      this.updateNetProfit();
    });
  }

  start(): void {
    if (this.isActive) {
      return;
    }

    this.isActive = true;
    this.logger.log('📊 Status Dashboard started');

    // Update dashboard every 5 seconds
    setInterval(() => {
      this.updateStats();
      if (process.env.SPLIT_SCREEN_DASHBOARD === 'true') {
        this.renderDashboard();
      }
    }, 5000);
  }

  stop(): void {
    this.isActive = false;
    this.logger.log('📊 Status Dashboard stopped');
  }

  private updateStats(): void {
    this.stats.uptime = Date.now() - this.startTime;
    
    if (this.stats.opportunitiesFound > 0) {
      this.stats.successRate = (this.stats.successfulExecutions / this.stats.opportunitiesFound) * 100;
    }

    if (this.stats.successfulExecutions > 0) {
      this.stats.averageProfit = this.stats.totalProfit / BigInt(this.stats.successfulExecutions);
    }

    this.updateNetProfit();
  }

  private updateNetProfit(): void {
    this.stats.netProfit = this.stats.totalProfit - this.stats.totalGasSpent;
  }

  private recordSuccessfulTransaction(transaction: SuccessfulTransaction): void {
    this.successfulTransactions.push(transaction);
    this.stats.successfulExecutions++;
    this.stats.totalProfit += transaction.profit;

    // Update strategy counters
    switch (transaction.type) {
      case 'flashloan':
      case 'dynamic-flashloan':
        this.stats.flashloanCount++;
        break;
      case 'arbitrage':
        this.stats.arbitrageCount++;
        break;
      case 'sandwich':
        this.stats.sandwichCount++;
        break;
      case 'mev-share':
        this.stats.frontrunCount++;
        break;
    }

    // Keep only last 100 transactions
    if (this.successfulTransactions.length > 100) {
      this.successfulTransactions = this.successfulTransactions.slice(-100);
    }

    this.updateStats();
  }

  private renderDashboard(): void {
    if (!this.isActive) {
      return;
    }

    const uptimeHours = (this.stats.uptime / (1000 * 60 * 60)).toFixed(2);
    const profitEth = ethers.formatEther(this.stats.totalProfit);
    const gasSpentEth = ethers.formatEther(this.stats.totalGasSpent);
    const netProfitEth = ethers.formatEther(this.stats.netProfit);

    const dashboard = `
${chalk.cyan('╔══════════════════════════════════════════════════════════════╗')}
${chalk.cyan('║')}                    ${chalk.bold.white('MEV Bot Dashboard')}                     ${chalk.cyan('║')}
${chalk.cyan('╠══════════════════════════════════════════════════════════════╣')}
${chalk.cyan('║')} ${chalk.yellow('Uptime:')} ${chalk.white(uptimeHours + 'h')}                                        ${chalk.cyan('║')}
${chalk.cyan('║')} ${chalk.yellow('Network:')} ${this.configService.chainId === 1 ? chalk.blue('Mainnet') : chalk.yellow('Testnet')}                                      ${chalk.cyan('║')}
${chalk.cyan('║')} ${chalk.yellow('Mode:')} ${this.configService.dryRun ? chalk.yellow('DRY RUN') : chalk.green('LIVE')}                                         ${chalk.cyan('║')}
${chalk.cyan('╠══════════════════════════════════════════════════════════════╣')}
${chalk.cyan('║')} ${chalk.bold.green('FINANCIAL STATS')}                                        ${chalk.cyan('║')}
${chalk.cyan('║')} Total Profit:     ${chalk.green(profitEth + ' ETH')}                          ${chalk.cyan('║')}
${chalk.cyan('║')} Gas Spent:        ${chalk.red(gasSpentEth + ' ETH')}                          ${chalk.cyan('║')}
${chalk.cyan('║')} Net Profit:       ${Number(netProfitEth) >= 0 ? chalk.green(netProfitEth + ' ETH') : chalk.red(netProfitEth + ' ETH')}                          ${chalk.cyan('║')}
${chalk.cyan('║')} Success Rate:     ${chalk.blue(this.stats.successRate.toFixed(1) + '%')}                              ${chalk.cyan('║')}
${chalk.cyan('╠══════════════════════════════════════════════════════════════╣')}
${chalk.cyan('║')} ${chalk.bold.blue('ACTIVITY STATS')}                                         ${chalk.cyan('║')}
${chalk.cyan('║')} Total Transactions:    ${chalk.white(this.stats.totalTransactions.toString())}                       ${chalk.cyan('║')}
${chalk.cyan('║')} Relevant Transactions: ${chalk.white(this.stats.relevantTransactions.toString())}                       ${chalk.cyan('║')}
${chalk.cyan('║')} Opportunities Found:   ${chalk.white(this.stats.opportunitiesFound.toString())}                       ${chalk.cyan('║')}
${chalk.cyan('║')} Successful Executions: ${chalk.white(this.stats.successfulExecutions.toString())}                       ${chalk.cyan('║')}
${chalk.cyan('╠══════════════════════════════════════════════════════════════╣')}
${chalk.cyan('║')} ${chalk.bold.purple('STRATEGY BREAKDOWN')}                                    ${chalk.cyan('║')}
${chalk.cyan('║')} Flashloans:  ${chalk.purple(this.stats.flashloanCount.toString())}                                     ${chalk.cyan('║')}
${chalk.cyan('║')} Arbitrage:   ${chalk.cyan(this.stats.arbitrageCount.toString())}                                     ${chalk.cyan('║')}
${chalk.cyan('║')} Sandwich:    ${chalk.red(this.stats.sandwichCount.toString())}                                     ${chalk.cyan('║')}
${chalk.cyan('║')} Frontrun:    ${chalk.yellow(this.stats.frontrunCount.toString())}                                     ${chalk.cyan('║')}
${chalk.cyan('╚══════════════════════════════════════════════════════════════╝')}
    `;

    // Clear screen and render dashboard
    console.clear();
    console.log(dashboard);

    // Show recent transactions
    if (this.successfulTransactions.length > 0) {
      console.log(chalk.bold.white('\n📈 Recent Successful Transactions:'));
      const recent = this.successfulTransactions.slice(-5);
      recent.forEach(tx => {
        const profit = ethers.formatEther(tx.profit);
        const time = new Date(tx.timestamp).toLocaleTimeString();
        console.log(`${chalk.gray(time)} ${chalk.green(tx.type)} ${chalk.white(profit + ' ETH')} ${chalk.blue(tx.confidence + '%')}`);
      });
    }
  }

  // Public methods for getting stats
  getStats(): DashboardStats {
    this.updateStats();
    return { ...this.stats };
  }

  getSuccessfulTransactions(): SuccessfulTransaction[] {
    return [...this.successfulTransactions];
  }

  getBotConfiguration(): BotConfiguration {
    return {
      primaryToken: this.configService.flashloanPrimaryToken,
      targetTokens: this.configService.flashloanTargetTokens,
      enableAllTokenPairs: this.configService.enableAllTokenPairs,
      flashloanDexPairs: this.configService.get('flashloanDexPairs', []),
      buyDex: this.configService.get('flashloanBuyDex', ''),
      sellDex: this.configService.get('flashloanSellDex', ''),
      enableCrossDex: this.configService.get('enableCrossDexArbitrage', false),
      minProfitWei: this.configService.minProfitWei,
      minArbitrageSpread: this.configService.get('minArbitrageSpread', '0'),
      minBackrunProfitEth: this.configService.get('minBackrunProfitEth', '0'),
      maxPositionSizeEth: this.configService.maxPositionSizeEth.toString(),
      maxGasCostEth: this.configService.get('maxGasCostEth', '0'),
      slippageTolerance: this.configService.slippageTolerance.toString(),
      arbitrageScanInterval: this.configService.get('arbitrageScanIntervalMs', 0).toString(),
      flashloanScanInterval: this.configService.get('flashloanScanIntervalMs', 0).toString(),
      chainId: this.configService.chainId,
      dryRun: this.configService.dryRun
    };
  }

  // Reset stats
  resetStats(): void {
    this.initializeStats();
    this.successfulTransactions = [];
    this.startTime = Date.now();
    this.logger.log('📊 Dashboard stats reset');
  }

  // Export stats to JSON
  exportStats(): string {
    return JSON.stringify({
      stats: this.getStats(),
      transactions: this.getSuccessfulTransactions(),
      configuration: this.getBotConfiguration(),
      exportTime: new Date().toISOString()
    }, null, 2);
  }
}
