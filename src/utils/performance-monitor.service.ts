import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as os from 'os';
import { performance } from 'perf_hooks';

export interface PerformanceMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
    heapUsed: number;
    heapTotal: number;
  };
  eventLoop: {
    lag: number;
  };
  workers?: {
    active: number;
    total: number;
    tasksProcessed: number;
    averageProcessingTime: number;
  };
  arbitrage?: {
    scanTime: number;
    pairsProcessed: number;
    opportunitiesFound: number;
    useWorkers: boolean;
  };
}

@Injectable()
export class PerformanceMonitorService {
  private readonly logger = new Logger(PerformanceMonitorService.name);
  
  private metrics: PerformanceMetrics[] = [];
  private maxHistorySize = 100;
  private monitoringInterval?: NodeJS.Timeout;
  private lastCpuUsage = process.cpuUsage();
  private lastEventLoopCheck = performance.now();

  constructor(private readonly eventEmitter: EventEmitter2) {}

  start(intervalMs: number = 2000): void {
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs);

    this.logger.log(`Performance monitoring started (interval: ${intervalMs}ms)`);
  }

  stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
      this.logger.log('Performance monitoring stopped');
    }
  }

  private collectMetrics(): void {
    const now = performance.now();
    
    // Calculate CPU usage
    const currentCpuUsage = process.cpuUsage(this.lastCpuUsage);
    const cpuPercent = (currentCpuUsage.user + currentCpuUsage.system) / 1000000; // Convert to seconds
    this.lastCpuUsage = process.cpuUsage();

    // Memory usage
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    // Event loop lag
    const eventLoopLag = now - this.lastEventLoopCheck;
    this.lastEventLoopCheck = now;

    const metrics: PerformanceMetrics = {
      cpu: {
        usage: cpuPercent,
        loadAverage: os.loadavg()
      },
      memory: {
        used: usedMem,
        total: totalMem,
        percentage: (usedMem / totalMem) * 100,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal
      },
      eventLoop: {
        lag: eventLoopLag
      }
    };

    this.addMetrics(metrics);
    this.checkPerformanceThresholds(metrics);
  }

  private addMetrics(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxHistorySize) {
      this.metrics = this.metrics.slice(-this.maxHistorySize);
    }

    // Emit metrics event
    this.eventEmitter.emit('performance.metrics', metrics);
  }

  private checkPerformanceThresholds(metrics: PerformanceMetrics): void {
    // Check CPU usage
    if (metrics.cpu.usage > 80) {
      this.logger.warn(`High CPU usage detected: ${metrics.cpu.usage.toFixed(2)}%`);
      this.eventEmitter.emit('performance.warning', {
        type: 'cpu',
        value: metrics.cpu.usage,
        threshold: 80
      });
    }

    // Check memory usage
    if (metrics.memory.percentage > 85) {
      this.logger.warn(`High memory usage detected: ${metrics.memory.percentage.toFixed(2)}%`);
      this.eventEmitter.emit('performance.warning', {
        type: 'memory',
        value: metrics.memory.percentage,
        threshold: 85
      });
    }

    // Check event loop lag
    if (metrics.eventLoop.lag > 100) { // 100ms lag threshold
      this.logger.warn(`High event loop lag detected: ${metrics.eventLoop.lag.toFixed(2)}ms`);
      this.eventEmitter.emit('performance.warning', {
        type: 'eventLoop',
        value: metrics.eventLoop.lag,
        threshold: 100
      });
    }
  }

  // Record arbitrage scan performance
  recordArbitrageScan(scanTime: number, pairsProcessed: number, opportunitiesFound: number, useWorkers: boolean): void {
    const latestMetrics = this.getLatestMetrics();
    if (latestMetrics) {
      latestMetrics.arbitrage = {
        scanTime,
        pairsProcessed,
        opportunitiesFound,
        useWorkers
      };
    }

    this.logger.debug('Arbitrage scan performance recorded', {
      scanTime: `${scanTime}ms`,
      pairsProcessed,
      opportunitiesFound,
      useWorkers
    });
  }

  // Record worker performance
  recordWorkerPerformance(active: number, total: number, tasksProcessed: number, averageProcessingTime: number): void {
    const latestMetrics = this.getLatestMetrics();
    if (latestMetrics) {
      latestMetrics.workers = {
        active,
        total,
        tasksProcessed,
        averageProcessingTime
      };
    }

    this.logger.debug('Worker performance recorded', {
      active,
      total,
      tasksProcessed,
      averageProcessingTime: `${averageProcessingTime}ms`
    });
  }

  // Get current metrics
  getCurrentMetrics(): PerformanceMetrics | null {
    return this.getLatestMetrics();
  }

  // Get latest metrics
  getLatestMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  // Get metrics history
  getMetricsHistory(count?: number): PerformanceMetrics[] {
    if (count) {
      return this.metrics.slice(-count);
    }
    return [...this.metrics];
  }

  // Get average metrics over time period
  getAverageMetrics(minutes: number = 5): Partial<PerformanceMetrics> | null {
    const cutoffTime = Date.now() - (minutes * 60 * 1000);
    const recentMetrics = this.metrics.filter(m => Date.now() - cutoffTime < minutes * 60 * 1000);
    
    if (recentMetrics.length === 0) {
      return null;
    }

    const avgCpuUsage = recentMetrics.reduce((sum, m) => sum + m.cpu.usage, 0) / recentMetrics.length;
    const avgMemoryPercentage = recentMetrics.reduce((sum, m) => sum + m.memory.percentage, 0) / recentMetrics.length;
    const avgEventLoopLag = recentMetrics.reduce((sum, m) => sum + m.eventLoop.lag, 0) / recentMetrics.length;

    return {
      cpu: {
        usage: avgCpuUsage,
        loadAverage: os.loadavg()
      },
      memory: {
        percentage: avgMemoryPercentage,
        used: 0,
        total: 0,
        heapUsed: 0,
        heapTotal: 0
      },
      eventLoop: {
        lag: avgEventLoopLag
      }
    };
  }

  // Get performance summary
  getPerformanceSummary(): {
    current: PerformanceMetrics | null;
    average: Partial<PerformanceMetrics> | null;
    warnings: number;
    uptime: number;
  } {
    const current = this.getCurrentMetrics();
    const average = this.getAverageMetrics(5);
    const uptime = process.uptime();
    
    // Count recent warnings (last 10 metrics)
    const recentMetrics = this.metrics.slice(-10);
    let warnings = 0;
    
    recentMetrics.forEach(metrics => {
      if (metrics.cpu.usage > 80) warnings++;
      if (metrics.memory.percentage > 85) warnings++;
      if (metrics.eventLoop.lag > 100) warnings++;
    });

    return {
      current,
      average,
      warnings,
      uptime
    };
  }

  // Check if system is under high load
  isHighLoad(): boolean {
    const current = this.getCurrentMetrics();
    if (!current) return false;

    return (
      current.cpu.usage > 80 ||
      current.memory.percentage > 85 ||
      current.eventLoop.lag > 100
    );
  }

  // Get system health status
  getHealthStatus(): 'healthy' | 'warning' | 'critical' {
    const current = this.getCurrentMetrics();
    if (!current) return 'warning';

    const criticalThresholds = {
      cpu: 95,
      memory: 95,
      eventLoop: 500
    };

    const warningThresholds = {
      cpu: 80,
      memory: 85,
      eventLoop: 100
    };

    // Check critical thresholds
    if (
      current.cpu.usage > criticalThresholds.cpu ||
      current.memory.percentage > criticalThresholds.memory ||
      current.eventLoop.lag > criticalThresholds.eventLoop
    ) {
      return 'critical';
    }

    // Check warning thresholds
    if (
      current.cpu.usage > warningThresholds.cpu ||
      current.memory.percentage > warningThresholds.memory ||
      current.eventLoop.lag > warningThresholds.eventLoop
    ) {
      return 'warning';
    }

    return 'healthy';
  }

  // Clear metrics history
  clearHistory(): void {
    this.metrics = [];
    this.logger.log('Performance metrics history cleared');
  }

  // Export metrics to JSON
  exportMetrics(): string {
    return JSON.stringify({
      metrics: this.metrics,
      summary: this.getPerformanceSummary(),
      exportTime: new Date().toISOString()
    }, null, 2);
  }
}
