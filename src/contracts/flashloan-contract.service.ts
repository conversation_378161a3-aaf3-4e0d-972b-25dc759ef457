import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';

@Injectable()
export class FlashloanContractService {
  private readonly logger = new Logger(FlashloanContractService.name);
  
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
    this.wallet = new ethers.Wallet(this.configService.privateKey, this.provider);
  }

  getAaveV3PoolContract(): ethers.Contract {
    const aavePoolAddress = '******************************************'; // Mainnet
    const poolABI = [
      'function flashLoan(address receiverAddress, address[] calldata assets, uint256[] calldata amounts, uint256[] calldata modes, address onBehalfOf, bytes calldata params, uint16 referralCode) external'
    ];

    return new ethers.Contract(aavePoolAddress, poolABI, this.wallet);
  }

  getBalancerVaultContract(): ethers.Contract {
    const balancerVaultAddress = '******************************************';
    const vaultABI = [
      'function flashLoan(address recipient, address[] memory tokens, uint256[] memory amounts, bytes memory userData) external'
    ];

    return new ethers.Contract(balancerVaultAddress, vaultABI, this.wallet);
  }

  getUniswapV3PoolContract(poolAddress: string): ethers.Contract {
    const poolABI = [
      'function flash(address recipient, uint256 amount0, uint256 amount1, bytes calldata data) external'
    ];

    return new ethers.Contract(poolAddress, poolABI, this.wallet);
  }

  async executeAaveFlashloan(
    asset: string,
    amount: bigint,
    params: string
  ): Promise<ethers.ContractTransactionResponse> {
    const pool = this.getAaveV3PoolContract();
    
    return await pool.flashLoan(
      this.configService.hybridFlashloanContract, // receiver
      [asset],
      [amount],
      [0], // mode 0 = no debt
      ethers.ZeroAddress,
      params,
      0 // referralCode
    );
  }

  async executeBalancerFlashloan(
    tokens: string[],
    amounts: bigint[],
    userData: string
  ): Promise<ethers.ContractTransactionResponse> {
    const vault = this.getBalancerVaultContract();
    
    return await vault.flashLoan(
      this.configService.hybridFlashloanContract, // recipient
      tokens,
      amounts,
      userData
    );
  }

  async executeUniswapV3Flash(
    poolAddress: string,
    amount0: bigint,
    amount1: bigint,
    data: string
  ): Promise<ethers.ContractTransactionResponse> {
    const pool = this.getUniswapV3PoolContract(poolAddress);
    
    return await pool.flash(
      this.configService.hybridFlashloanContract, // recipient
      amount0,
      amount1,
      data
    );
  }
}
