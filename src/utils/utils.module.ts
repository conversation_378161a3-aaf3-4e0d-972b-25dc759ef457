import { Module } from '@nestjs/common';
import { StatusDashboardService } from './status-dashboard.service';
import { PerformanceMonitorService } from './performance-monitor.service';
import { LoggerService } from './logger.service';
import { EnhancedLoggerService } from './enhanced-logger.service';

@Module({
  providers: [
    StatusDashboardService,
    PerformanceMonitorService,
    LoggerService,
    EnhancedLoggerService,
  ],
  exports: [
    StatusDashboardService,
    PerformanceMonitorService,
    LoggerService,
    EnhancedLoggerService,
  ],
})
export class UtilsModule {}
