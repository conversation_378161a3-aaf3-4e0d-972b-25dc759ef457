import * as <PERSON><PERSON> from 'joi';

export const configValidationSchema = Joi.object({
  // Network configuration
  CHAIN_ID: Joi.number().default(11155111),
  RPC_URL: Joi.string().default('http://localhost:8545'),
  PRIVATE_KEY: Joi.string().required(),
  FLASHBOTS_SIGNER_KEY: Joi.string().optional(),

  // MEV configuration
  MIN_PROFIT_WEI: Joi.string().default('100000000000000'),
  MAX_GAS_PRICE_GWEI: Joi.number().default(100),
  MAX_PRIORITY_FEE_GWEI: Joi.number().default(5),
  SLIPPAGE_TOLERANCE: Joi.number().default(0.005),

  // Strategy configuration
  ENABLE_SANDWICH_ATTACKS: Joi.boolean().default(false),
  ENABLE_FRONT_RUNNING: Joi.boolean().default(false),
  ENABLE_ARBITRAGE: Joi.boolean().default(false),
  ENABLE_FLASHLOAN_ATTACKS: Joi.boolean().default(true),
  ENABLE_MULTI_BLOCK_ATTACKS: Joi.boolean().default(false),

  // Flashloan configuration
  FLASHLOAN_TOKENS: Joi.string().default('USDC,WETH,USDT,DAI'),
  FLASHLOAN_PRIMARY_TOKEN: Joi.string().default('USDC'),
  FLASHLOAN_TARGET_TOKENS: Joi.string().default('WETH,USDT,DAI'),
  ENABLE_ALL_TOKEN_PAIRS: Joi.boolean().default(true),
  UNISWAP_V3_TRADING_PAIRS: Joi.string().default('WETH/USDC,WETH/USDT,WBTC/WETH,DAI/USDC'),
  UNISWAP_V3_FEE_TIERS: Joi.string().default('500,3000,10000'),

  // Contract addresses
  HYBRID_FLASHLOAN_CONTRACT: Joi.string().optional(),
  BALANCER_FLASHLOAN_CONTRACT: Joi.string().optional(),
  AAVE_FLASHLOAN_CONTRACT: Joi.string().optional(),

  // Flashbots configuration
  ENABLE_FLASHBOTS: Joi.boolean().default(false),
  FLASHBOTS_RELAY_URL: Joi.string().default('https://relay.flashbots.net'),
  FLASHBOTS_AUTH_KEY: Joi.string().optional(),

  // MEV-Share configuration
  ENABLE_MEV_SHARE: Joi.boolean().default(false),
  MEV_SHARE_STREAM_URL: Joi.string().default('https://mev-share.flashbots.net'),

  // Risk management
  MAX_POSITION_SIZE_ETH: Joi.number().default(10),
  EMERGENCY_STOP: Joi.boolean().default(false),
  DRY_RUN: Joi.boolean().default(false),

  // Logging
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug', 'verbose').default('info'),
  LOG_TO_FILE: Joi.boolean().default(true),

  // Optional environment variables
  MEMPOOL_WEBSOCKET_URL: Joi.string().optional(),
  ENABLE_FLASHBOTS_MEMPOOL: Joi.boolean().default(true),
  ENABLE_ETHERS_MEMPOOL: Joi.boolean().default(true),
  BLOCKNATIVE_API_KEY: Joi.string().optional(),
  ENABLE_BLOCKNATIVE_GAS: Joi.boolean().default(false),
  ENABLE_0X_API_GAS: Joi.boolean().default(true),
  ENABLE_ETH_GAS_STATION: Joi.boolean().default(true),
  FALLBACK_GAS_PRICE_GWEI: Joi.number().default(20),
  ARBITRAGE_SCAN_INTERVAL_MS: Joi.number().default(0),
  FLASHLOAN_SCAN_INTERVAL_MS: Joi.number().default(0),
  ENABLE_TEST_MODE: Joi.boolean().default(false),
  MOCK_OPPORTUNITIES: Joi.boolean().default(false),
  TEST_LIQUIDITY_SETUP: Joi.boolean().default(false),
});
