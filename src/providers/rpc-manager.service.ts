import { Injectable, Logger } from '@nestjs/common';
import { ConfigurationService } from '../config/configuration.service';
import { ethers } from 'ethers';

export interface RPCProvider {
  name: string;
  http: string;
  ws: string;
  priority: number;
}

@Injectable()
export class RPCManagerService {
  private readonly logger = new Logger(RPCManagerService.name);
  private providers: RPCProvider[] = [];
  private currentProvider: RPCProvider | null = null;
  private httpProvider: ethers.JsonRpcProvider | null = null;
  private wsProvider: ethers.WebSocketProvider | null = null;
  private failedProviders: Set<string> = new Set();

  constructor(private readonly configService: ConfigurationService) {
    this.initializeProviders();
    this.selectBestProvider();
  }

  private initializeProviders(): void {
    const chainId = this.configService.chainId;
    const isMainnet = chainId === 1;
    const isTestnet = chainId === 11155111; // Sepolia
    const isHardhat = chainId === 31337; // Hardhat local network

    // Alchemy (Best for MEV - High rate limits)
    if (process.env.ALCHEMY_API_KEY) {
      this.providers.push({
        name: 'Alchemy',
        http: isMainnet 
          ? `https://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`
          : `https://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
        ws: isMainnet
          ? `wss://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`
          : `wss://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
        priority: 1
      });
    }

    // QuickNode (MEV Optimized - No rate limits on paid plans)
    if (process.env.QUICKNODE_ENDPOINT) {
      this.providers.push({
        name: 'QuickNode',
        http: process.env.QUICKNODE_ENDPOINT,
        ws: process.env.QUICKNODE_ENDPOINT.replace('https://', 'wss://'),
        priority: 2
      });
    }

    // Infura (Reliable fallback)
    if (process.env.INFURA_PROJECT_ID) {
      this.providers.push({
        name: 'Infura',
        http: isMainnet
          ? `https://mainnet.infura.io/v3/${process.env.INFURA_PROJECT_ID}`
          : `https://sepolia.infura.io/v3/${process.env.INFURA_PROJECT_ID}`,
        ws: isMainnet
          ? `wss://mainnet.infura.io/ws/v3/${process.env.INFURA_PROJECT_ID}`
          : `wss://sepolia.infura.io/ws/v3/${process.env.INFURA_PROJECT_ID}`,
        priority: 3
      });
    }

    // Ankr (Free tier with good performance)
    this.providers.push({
      name: 'Ankr',
      http: isMainnet
        ? 'https://rpc.ankr.com/eth'
        : 'https://rpc.ankr.com/eth_sepolia',
      ws: isMainnet
        ? 'wss://rpc.ankr.com/eth/ws'
        : 'wss://rpc.ankr.com/eth_sepolia/ws',
      priority: 4
    });

    // Hardhat local node
    if (isHardhat || process.env.NODE_ENV === 'development') {
      this.providers.push({
        name: 'Hardhat',
        http: 'http://localhost:8545',
        ws: 'ws://localhost:8545',
        priority: 0 // Highest priority for local development
      });
    }

    // Custom RPC URL from environment
    if (this.configService.rpcUrl && this.configService.rpcUrl !== 'http://localhost:8545') {
      this.providers.push({
        name: 'Custom',
        http: this.configService.rpcUrl,
        ws: this.configService.rpcUrl.replace('http', 'ws'),
        priority: 5
      });
    }

    // Sort providers by priority
    this.providers.sort((a, b) => a.priority - b.priority);
    
    this.logger.log(`Initialized ${this.providers.length} RPC providers`);
  }

  private selectBestProvider(): void {
    const availableProviders = this.providers.filter(p => !this.failedProviders.has(p.name));
    
    if (availableProviders.length === 0) {
      this.logger.error('No available RPC providers');
      throw new Error('No available RPC providers');
    }

    this.currentProvider = availableProviders[0];
    this.logger.log(`Selected RPC provider: ${this.currentProvider.name}`);
  }

  getHttpProvider(): ethers.JsonRpcProvider {
    if (!this.httpProvider && this.currentProvider) {
      this.httpProvider = new ethers.JsonRpcProvider(this.currentProvider.http);
      this.logger.log(`Created HTTP provider for ${this.currentProvider.name}`);
    }
    
    if (!this.httpProvider) {
      throw new Error('No HTTP provider available');
    }
    
    return this.httpProvider;
  }

  getWebSocketProvider(): ethers.WebSocketProvider {
    if (!this.wsProvider && this.currentProvider) {
      this.wsProvider = new ethers.WebSocketProvider(this.currentProvider.ws);
      this.logger.log(`Created WebSocket provider for ${this.currentProvider.name}`);
    }
    
    if (!this.wsProvider) {
      throw new Error('No WebSocket provider available');
    }
    
    return this.wsProvider;
  }

  async switchProvider(): Promise<void> {
    if (!this.currentProvider) {
      throw new Error('No current provider to switch from');
    }

    this.logger.warn(`Switching from failed provider: ${this.currentProvider.name}`);
    this.failedProviders.add(this.currentProvider.name);
    
    // Clean up current providers
    if (this.httpProvider) {
      this.httpProvider.destroy();
      this.httpProvider = null;
    }
    
    if (this.wsProvider) {
      this.wsProvider.destroy();
      this.wsProvider = null;
    }

    this.selectBestProvider();
  }

  async testProvider(provider: RPCProvider): Promise<boolean> {
    try {
      const testProvider = new ethers.JsonRpcProvider(provider.http);
      await testProvider.getNetwork();
      testProvider.destroy();
      return true;
    } catch (error) {
      this.logger.error(`Provider ${provider.name} test failed:`, error);
      return false;
    }
  }

  getCurrentProvider(): RPCProvider | null {
    return this.currentProvider;
  }

  getAvailableProviders(): RPCProvider[] {
    return this.providers.filter(p => !this.failedProviders.has(p.name));
  }

  async healthCheck(): Promise<{ provider: string; status: string; latency?: number }[]> {
    const results = [];
    
    for (const provider of this.providers) {
      const start = Date.now();
      try {
        const testProvider = new ethers.JsonRpcProvider(provider.http);
        await testProvider.getNetwork();
        const latency = Date.now() - start;
        testProvider.destroy();
        
        results.push({
          provider: provider.name,
          status: 'healthy',
          latency
        });
      } catch (error) {
        results.push({
          provider: provider.name,
          status: 'unhealthy'
        });
      }
    }
    
    return results;
  }
}
