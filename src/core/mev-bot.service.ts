import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ethers } from 'ethers';
import { FlashbotsBundleProvider } from '@flashbots/ethers-provider-bundle';

// Services
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';
import { MempoolMonitorService } from '../mempool/mempool-monitor.service';
import { BlockEventMonitorService } from '../events/block-event-monitor.service';
import { SandwichStrategyService } from '../strategies/sandwich-strategy.service';
import { ArbitrageStrategyService } from '../strategies/arbitrage-strategy.service';
import { FlashloanStrategyService } from '../strategies/flashloan-strategy.service';
import { MEVShareFlashloanStrategyService } from '../strategies/mev-share-flashloan-strategy.service';
import { UniswapV3FlashSwapStrategyService } from '../strategies/uniswap-v3-flash-strategy.service';
import { DynamicFlashloanStrategyService } from '../strategies/dynamic-flashloan-strategy.service';
import { MEVShareEventMonitorService } from '../mev-share/mev-share-event-monitor.service';
import { FlashbotsBundleManagerService } from '../flashbots/flashbots-bundle-manager.service';
import { AdvancedGasEstimatorService } from '../gas/advanced-gas-estimator.service';
import { FlashbotsExecutorService } from '../execution/flashbots-executor.service';
import { BundleSimulatorService } from '../simulation/bundle-simulator.service';
import { GasOptimizerService } from '../gas/gas-optimizer.service';
import { StatusDashboardService } from '../utils/status-dashboard.service';
import { PerformanceMonitorService } from '../utils/performance-monitor.service';

// Types
import { BotState, MEVOpportunity, Transaction, Bundle, RiskMetrics } from '../types';

@Injectable()
export class MEVBotService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MEVBotService.name);
  
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;

  private state: BotState;
  private riskMetrics: RiskMetrics;
  private opportunities: MEVOpportunity[] = [];
  private executedBundles: Bundle[] = [];

  private readonly MAX_OPPORTUNITIES = 1000;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly eventEmitter: EventEmitter2,
    private readonly rpcManager: RPCManagerService,
    private readonly mempoolMonitor: MempoolMonitorService,
    private readonly blockEventMonitor: BlockEventMonitorService,
    private readonly sandwichStrategy: SandwichStrategyService,
    private readonly arbitrageStrategy: ArbitrageStrategyService,
    private readonly flashloanStrategy: FlashloanStrategyService,
    private readonly uniswapV3FlashSwapStrategy: UniswapV3FlashSwapStrategyService,
    private readonly dynamicFlashloanStrategy: DynamicFlashloanStrategyService,
    private readonly flashbotsManager: FlashbotsBundleManagerService,
    private readonly advancedGasEstimator: AdvancedGasEstimatorService,
    private readonly flashbotsExecutor: FlashbotsExecutorService,
    private readonly simulator: BundleSimulatorService,
    private readonly gasOptimizer: GasOptimizerService,
    private readonly statusDashboard: StatusDashboardService,
    private readonly performanceMonitor: PerformanceMonitorService,
    private readonly mevShareMonitor?: MEVShareEventMonitorService,
    private readonly mevShareFlashloanStrategy?: MEVShareFlashloanStrategyService,
  ) {}

  async onModuleInit() {
    this.logger.log('🤖 Initializing MEV Bot Service...');
    
    // Initialize providers
    this.provider = this.rpcManager.getHttpProvider();
    this.wallet = new ethers.Wallet(this.configService.privateKey, this.provider);

    // Initialize state
    this.state = {
      isRunning: false,
      totalProfit: BigInt(0),
      successfulTrades: 0,
      failedTrades: 0,
      lastActivity: 0,
      emergencyStop: this.configService.emergencyStop
    };

    this.riskMetrics = {
      maxDrawdown: BigInt(0),
      winRate: 0,
      averageProfit: BigInt(0),
      totalGasSpent: BigInt(0),
      profitFactor: 0
    };

    this.setupEventListeners();
    this.logger.log('✅ MEV Bot Service initialized');
  }

  async onModuleDestroy() {
    this.logger.log('🛑 Shutting down MEV Bot Service...');
    await this.stop();
  }

  private setupEventListeners(): void {
    // Listen for relevant transactions from mempool
    this.eventEmitter.on('mempool.pendingTransaction', async (transaction: any) => {
      await this.handleRelevantTransaction(transaction);
    });

    // Listen for opportunities found
    this.eventEmitter.on('opportunity.found', async (opportunity: any) => {
      await this.handleOpportunityFound(opportunity);
    });

    this.eventEmitter.on('bundle.executed', this.handleBundleExecuted.bind(this));
    this.eventEmitter.on('error', this.handleError.bind(this));
  }

  async start(): Promise<void> {
    if (this.state.isRunning) {
      this.logger.warn('MEV Bot is already running');
      return;
    }

    this.logger.log('🚀 Starting MEV Bot...');

    try {
      // Validate configuration
      await this.validateConfiguration();

      // Start performance monitoring
      this.performanceMonitor.start();

      // Start status dashboard
      this.statusDashboard.start();

      // Start mempool monitoring
      await this.mempoolMonitor.start();

      // Start block event monitoring
      await this.blockEventMonitor.start();

      // Initialize MEV-Share if enabled
      if (this.configService.enableMevShare && this.mevShareMonitor) {
        await this.mevShareMonitor.start();
        if (this.mevShareFlashloanStrategy) {
          this.dynamicFlashloanStrategy.setMEVShareStrategy(this.mevShareFlashloanStrategy);
        }
      }

      this.state.isRunning = true;
      this.state.lastActivity = Date.now();

      this.logger.log('✅ MEV Bot started successfully');
      this.eventEmitter.emit('bot.started');

    } catch (error) {
      this.logger.error('❌ Failed to start MEV Bot:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.state.isRunning) {
      this.logger.warn('MEV Bot is not running');
      return;
    }

    this.logger.log('🛑 Stopping MEV Bot...');

    try {
      this.state.isRunning = false;

      // Stop all monitoring services
      await this.mempoolMonitor.stop();
      await this.blockEventMonitor.stop();
      
      if (this.mevShareMonitor) {
        await this.mevShareMonitor.stop();
      }

      // Stop performance monitoring
      this.performanceMonitor.stop();

      this.logger.log('✅ MEV Bot stopped successfully');
      this.eventEmitter.emit('bot.stopped');

    } catch (error) {
      this.logger.error('❌ Error stopping MEV Bot:', error);
      throw error;
    }
  }

  emergencyStop(): void {
    this.logger.error('🚨 EMERGENCY STOP ACTIVATED');
    this.state.emergencyStop = true;
    this.state.isRunning = false;
    this.eventEmitter.emit('bot.emergencyStop');
  }

  private async validateConfiguration(): Promise<void> {
    if (!this.configService.privateKey) {
      throw new Error('Private key is required');
    }

    if (!this.configService.rpcUrl) {
      throw new Error('RPC URL is required');
    }

    // Test provider connection
    try {
      await this.provider.getNetwork();
      this.logger.log('✅ Provider connection validated');
    } catch (error) {
      throw new Error(`Failed to connect to provider: ${error}`);
    }
  }

  private async handleRelevantTransaction(transaction: Transaction): Promise<void> {
    if (!this.state.isRunning || this.state.emergencyStop) {
      return;
    }

    try {
      this.state.lastActivity = Date.now();

      // Strategy-specific transaction handling
      if (this.configService.enableSandwichAttacks) {
        await this.sandwichStrategy.analyzeTransaction(transaction);
      }

      if (this.configService.enableArbitrage) {
        await this.arbitrageStrategy.analyzeTransaction(transaction);
      }

      if (this.configService.enableFlashloanAttacks) {
        await this.flashloanStrategy.analyzeTransaction(transaction);
      }

    } catch (error) {
      this.logger.error('Error handling transaction:', error);
      this.handleError(error);
    }
  }

  private async handleOpportunityFound(opportunity: MEVOpportunity): Promise<void> {
    if (!this.state.isRunning || this.state.emergencyStop) {
      return;
    }

    try {
      // Add to opportunities list
      this.opportunities.push(opportunity);
      
      // Keep only recent opportunities
      if (this.opportunities.length > this.MAX_OPPORTUNITIES) {
        this.opportunities = this.opportunities.slice(-this.MAX_OPPORTUNITIES);
      }

      // Execute opportunity if profitable
      if (opportunity.profitability > 0) {
        await this.executeOpportunity(opportunity);
      }

    } catch (error) {
      this.logger.error('Error handling opportunity:', error);
      this.handleError(error);
    }
  }

  private async executeOpportunity(opportunity: MEVOpportunity): Promise<void> {
    // Implementation will be added in the next chunk
    this.logger.debug(`Executing opportunity: ${opportunity.type}`);
  }

  private handleBundleExecuted(bundle: Bundle): void {
    this.executedBundles.push(bundle);
    this.updateMetrics(bundle);
  }

  private updateMetrics(bundle: Bundle): void {
    // Update bot metrics based on bundle execution
    if (bundle.profit && bundle.profit > 0n) {
      this.state.successfulTrades++;
      this.state.totalProfit += bundle.profit;
    } else {
      this.state.failedTrades++;
    }

    // Update risk metrics
    this.riskMetrics.winRate = this.state.successfulTrades / (this.state.successfulTrades + this.state.failedTrades);
  }

  private handleError(error: any): void {
    this.logger.error('MEV Bot error:', error);
    this.eventEmitter.emit('bot.error', error);
  }

  // Getters for state
  getState(): BotState {
    return { ...this.state };
  }

  getRiskMetrics(): RiskMetrics {
    return { ...this.riskMetrics };
  }

  getOpportunities(): MEVOpportunity[] {
    return [...this.opportunities];
  }

  getExecutedBundles(): Bundle[] {
    return [...this.executedBundles];
  }
}
