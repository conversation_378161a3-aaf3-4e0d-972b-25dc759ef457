import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ethers } from 'ethers';
import { FlashbotsBundleProvider } from '@flashbots/ethers-provider-bundle';

// Services
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';
import { MempoolMonitorService } from '../mempool/mempool-monitor.service';
import { BlockEventMonitorService } from '../events/block-event-monitor.service';
import { SandwichStrategyService } from '../strategies/sandwich-strategy.service';
import { ArbitrageStrategyService } from '../strategies/arbitrage-strategy.service';
import { FlashloanStrategyService } from '../strategies/flashloan-strategy.service';
import { MEVShareFlashloanStrategyService } from '../strategies/mev-share-flashloan-strategy.service';
import { UniswapV3FlashSwapStrategyService } from '../strategies/uniswap-v3-flash-strategy.service';
import { DynamicFlashloanStrategyService } from '../strategies/dynamic-flashloan-strategy.service';
import { MEVShareEventMonitorService } from '../mev-share/mev-share-event-monitor.service';
import { FlashbotsBundleManagerService } from '../flashbots/flashbots-bundle-manager.service';
import { AdvancedGasEstimatorService } from '../gas/advanced-gas-estimator.service';
import { FlashbotsExecutorService } from '../execution/flashbots-executor.service';
import { BundleSimulatorService } from '../simulation/bundle-simulator.service';
import { GasOptimizerService } from '../gas/gas-optimizer.service';
import { StatusDashboardService } from '../utils/status-dashboard.service';
import { PerformanceMonitorService } from '../utils/performance-monitor.service';
import { EnhancedLoggerService } from '../utils/enhanced-logger.service';

// Types
import { BotState, MEVOpportunity, Transaction, Bundle, RiskMetrics, BlockData } from '../types';

@Injectable()
export class MEVBotService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MEVBotService.name);

  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;

  // Bot state
  private state: BotState;
  private riskMetrics: RiskMetrics;
  private opportunities: MEVOpportunity[] = [];
  private executedBundles: Bundle[] = [];
  private readonly MAX_OPPORTUNITIES = 1000;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly eventEmitter: EventEmitter2,
    private readonly rpcManager: RPCManagerService,
    private readonly mempoolMonitor: MempoolMonitorService,
    private readonly blockEventMonitor: BlockEventMonitorService,
    private readonly sandwichStrategy: SandwichStrategyService,
    private readonly arbitrageStrategy: ArbitrageStrategyService,
    private readonly flashloanStrategy: FlashloanStrategyService,
    private readonly uniswapV3FlashSwapStrategy: UniswapV3FlashSwapStrategyService,
    private readonly dynamicFlashloanStrategy: DynamicFlashloanStrategyService,
    private readonly flashbotsManager: FlashbotsBundleManagerService,
    private readonly advancedGasEstimator: AdvancedGasEstimatorService,
    private readonly flashbotsExecutor: FlashbotsExecutorService,
    private readonly simulator: BundleSimulatorService,
    private readonly gasOptimizer: GasOptimizerService,
    private readonly statusDashboard: StatusDashboardService,
    private readonly performanceMonitor: PerformanceMonitorService,
    private readonly enhancedLogger: EnhancedLoggerService,
    private readonly mevShareMonitor?: MEVShareEventMonitorService,
    private readonly mevShareFlashloanStrategy?: MEVShareFlashloanStrategyService,
  ) {
    // Initialize state
    this.state = {
      isRunning: false,
      totalProfit: BigInt(0),
      successfulTrades: 0,
      failedTrades: 0,
      lastActivity: 0,
      emergencyStop: false
    };

    this.riskMetrics = {
      maxDrawdown: BigInt(0),
      winRate: 0,
      averageProfit: BigInt(0),
      totalGasSpent: BigInt(0),
      profitFactor: 0
    };
  }

  async onModuleInit() {
    this.logger.log('🤖 Initializing MEV Bot Service...');

    // Initialize providers
    this.provider = this.rpcManager.getHttpProvider();
    this.wallet = new ethers.Wallet(this.configService.privateKey, this.provider);

    this.setupEventListeners();
    this.logger.log('✅ MEV Bot Service initialized');
  }

  async onModuleDestroy() {
    this.logger.log('🛑 Shutting down MEV Bot Service...');
    await this.stop();
  }

  private setupEventListeners(): void {
    // Listen for relevant transactions from mempool
    this.eventEmitter.on('mempool.pendingTransaction', async (transaction: any) => {
      await this.handleRelevantTransaction(transaction);
    });

    // Listen for opportunities found
    this.eventEmitter.on('opportunity.found', async (opportunity: any) => {
      await this.handleOpportunityFound(opportunity);
    });

    this.eventEmitter.on('bundle.executed', this.handleBundleExecuted.bind(this));
    this.eventEmitter.on('error', this.handleError.bind(this));
  }

  async start(): Promise<void> {
    if (this.state.isRunning) {
      this.logger.warn('MEV Bot is already running');
      return;
    }

    this.logger.log('🚀 Starting MEV Bot...');

    try {
      // Validate configuration
      await this.validateConfiguration();

      // Initialize Flashbots
      await this.initializeFlashbots();

      // Initialize MEV-Share if enabled
      if (this.configService.enableMevShare && this.mevShareMonitor && this.mevShareFlashloanStrategy) {
        this.logger.log('🔄 Initializing MEV-Share integration...');
        await this.initializeMEVShare();
      }

      // Check wallet balance
      await this.checkWalletBalance();

      // Start performance monitoring
      this.performanceMonitor.start();

      // Start status dashboard
      this.statusDashboard.start();

      // Start mempool monitoring
      await this.mempoolMonitor.start();

      // Start block event monitoring
      await this.blockEventMonitor.start();

      // Setup event-driven arbitrage monitoring
      this.logger.log('Setting up event-driven arbitrage monitoring...');
      this.setupEventDrivenArbitrage();

      // Setup event-driven block tracking
      this.setupEventDrivenBlockTracking();

      // Setup ETH balance monitoring
      this.setupBalanceMonitoring();

      this.state.isRunning = true;
      this.state.lastActivity = Date.now();

      this.logger.log('✅ MEV Bot started successfully');
      this.eventEmitter.emit('bot.started');

    } catch (error) {
      this.logger.error('❌ Failed to start MEV Bot:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.state.isRunning) {
      this.logger.warn('MEV Bot is not running');
      return;
    }

    this.logger.log('🛑 Stopping MEV Bot...');

    try {
      this.state.isRunning = false;

      // Stop all monitoring services
      await this.mempoolMonitor.stop();
      await this.blockEventMonitor.stop();
      
      if (this.mevShareMonitor) {
        await this.mevShareMonitor.stop();
      }

      // Stop performance monitoring
      this.performanceMonitor.stop();

      this.logger.log('✅ MEV Bot stopped successfully');
      this.eventEmitter.emit('bot.stopped');

    } catch (error) {
      this.logger.error('❌ Error stopping MEV Bot:', error);
      throw error;
    }
  }



  private async validateConfiguration(): Promise<void> {
    if (!this.configService.privateKey) {
      throw new Error('Private key is required');
    }

    if (!this.configService.rpcUrl) {
      throw new Error('RPC URL is required');
    }

    // Test provider connection
    try {
      await this.provider.getNetwork();
      this.logger.log('✅ Provider connection validated');
    } catch (error) {
      throw new Error(`Failed to connect to provider: ${error}`);
    }
  }

  private async handleRelevantTransaction(transaction: Transaction): Promise<void> {
    if (!this.state.isRunning || this.state.emergencyStop) {
      return;
    }

    try {
      this.state.lastActivity = Date.now();

      // Strategy-specific transaction handling
      if (this.configService.enableSandwichAttacks) {
        await this.sandwichStrategy.analyzeTransaction(transaction);
      }

      if (this.configService.enableArbitrage) {
        await this.arbitrageStrategy.analyzeTransaction(transaction);
      }

      if (this.configService.enableFlashloanAttacks) {
        await this.flashloanStrategy.analyzeTransaction(transaction);
      }

    } catch (error) {
      this.logger.error('Error handling transaction:', error);
      this.handleError(error);
    }
  }

  private async handleOpportunityFound(opportunity: MEVOpportunity): Promise<void> {
    if (!this.state.isRunning || this.state.emergencyStop) {
      return;
    }

    try {
      // Add to opportunities list
      this.opportunities.push(opportunity);
      
      // Keep only recent opportunities
      if (this.opportunities.length > this.MAX_OPPORTUNITIES) {
        this.opportunities = this.opportunities.slice(-this.MAX_OPPORTUNITIES);
      }

      // Execute opportunity if profitable
      if (Number(opportunity.estimatedProfit) > 0) {
        await this.executeOpportunity(opportunity);
      }

    } catch (error) {
      this.logger.error('Error handling opportunity:', error);
      this.handleError(error);
    }
  }

  private async executeOpportunity(opportunity: MEVOpportunity): Promise<void> {
    // Implementation will be added in the next chunk
    this.logger.debug(`Executing opportunity: ${opportunity.type}`);
  }

  private handleBundleExecuted(bundle: Bundle): void {
    this.executedBundles.push(bundle);
    this.updateMetrics(bundle);
  }

  private updateMetrics(bundle: Bundle): void {
    // Update bot metrics based on bundle execution
    // For now, just count successful vs failed bundles
    // Profit tracking would need to be implemented based on actual execution results
    if (bundle.transactions.length > 0) {
      this.state.successfulTrades++;
    } else {
      this.state.failedTrades++;
    }

    this.updateRiskMetrics();
  }

  private updateRiskMetrics(): void {
    const totalTrades = this.state.successfulTrades + this.state.failedTrades;

    if (totalTrades > 0) {
      this.riskMetrics.winRate = (this.state.successfulTrades / totalTrades) * 100;
      this.riskMetrics.averageProfit = BigInt(this.state.totalProfit.toString()) / BigInt(totalTrades);
    }

    // Calculate profit factor (gross profit / gross loss)
    // This would need more detailed tracking of individual trade results
    this.riskMetrics.profitFactor = this.riskMetrics.winRate / 100;
  }

  private handleError(error: any): void {
    this.logger.error('MEV Bot error:', error);
    this.eventEmitter.emit('bot.error', error);
  }

  // Getters for state
  getState(): BotState {
    return { ...this.state };
  }

  getRiskMetrics(): RiskMetrics {
    return { ...this.riskMetrics };
  }

  getOpportunities(): MEVOpportunity[] {
    return [...this.opportunities];
  }

  getExecutedBundles(): Bundle[] {
    return [...this.executedBundles];
  }

  // Additional methods from original bot.ts
  private async initializeFlashbots(): Promise<void> {
    try {
      this.enhancedLogger.systemStatus('🔧 Initializing Flashbots integration...');

      // Initialize Flashbots bundle manager
      if (this.flashbotsManager.isEnabled()) {
        this.enhancedLogger.systemStatus('✅ Flashbots provider initialized successfully');

        // Get execution stats
        const stats = await this.flashbotsExecutor.getExecutionStats();
        this.enhancedLogger.systemStatus(`   Recommended Strategy: ${stats.recommendedStrategy}`);
        this.enhancedLogger.systemStatus(`   Network Congestion: ${(stats.networkCongestion * 100).toFixed(1)}%`);
      } else {
        this.enhancedLogger.systemStatus('⚠️  Flashbots not available (testnet or initialization failed)');
        this.enhancedLogger.systemStatus('   Will use regular mempool execution');
      }

      // Display simulation mode status
      if (this.configService.get('simulationMode', false)) {
        this.enhancedLogger.systemStatus('🎭 SIMULATION MODE ENABLED');
        this.enhancedLogger.systemStatus('   Opportunities will be detected and analyzed');
        this.enhancedLogger.systemStatus('   No real transactions will be executed');
        this.enhancedLogger.systemStatus('   Perfect for mainnet monitoring without gas costs');
      }

    } catch (error) {
      this.logger.error('MEVBot.initializeFlashbots', error);
      this.enhancedLogger.systemStatus('⚠️  Flashbots initialization failed, using mempool fallback');
    }
  }

  private async initializeMEVShare(): Promise<void> {
    try {
      if (!this.mevShareMonitor || !this.mevShareFlashloanStrategy) {
        this.enhancedLogger.systemStatus('⚠️  MEV-Share components not initialized');
        return;
      }

      // Initialize MEV-Share monitor
      await this.mevShareMonitor.start();

      if (this.mevShareMonitor.getStatus().isRunning) {
        // Start MEV-Share monitoring
        this.logger.info('MEV-Share integration initialized successfully');

        const status = this.mevShareFlashloanStrategy.getStatus();
        this.logger.info('MEV-Share configuration', {
          gasProtection: status.gasProtection,
          maxGasCost: status.maxGasCost
        });
      } else {
        this.logger.warn('MEV-Share not available (requires mainnet)');
      }

    } catch (error) {
      this.logger.error('MEVBot.initializeMEVShare', error);
      this.logger.error('MEV-Share initialization failed');
    }
  }

  private async checkWalletBalance(): Promise<void> {
    const balance = await this.wallet.provider!.getBalance(this.wallet.address);
    const balanceEth = Number(ethers.formatEther(balance));

    this.enhancedLogger.walletBalance(balanceEth, 'ETH');

    if (balanceEth < 0.1) {
      this.enhancedLogger.warning('Low wallet balance - consider adding more ETH');
    }

    if (balanceEth < 0.01) {
      throw new Error('Insufficient wallet balance for MEV operations');
    }
  }

  private setupEventDrivenArbitrage(): void {
    if (!this.configService.enableArbitrage) {
      return;
    }

    // Listen for new blocks to trigger arbitrage analysis
    this.blockEventMonitor.on('newBlock', async (blockData: BlockData) => {
      if (!this.state.isRunning || this.state.emergencyStop) {
        return;
      }
      await this.analyzeArbitrageOpportunities('newBlock');
    });

    // Listen for relevant transactions to trigger immediate arbitrage analysis
    this.mempoolMonitor.on('pendingTransaction', async (transaction: any) => {
      if (!this.state.isRunning || this.state.emergencyStop) {
        return;
      }
      await this.analyzeArbitrageOpportunities('pendingTransaction', transaction);
    });

    this.logger.log('Event-driven arbitrage monitoring started');
  }

  private async analyzeArbitrageOpportunities(trigger: string, transaction?: any): Promise<void> {
    try {
      const startTime = Date.now();

      // Scan for regular arbitrage opportunities
      const routes = await this.arbitrageStrategy.scanForArbitrageOpportunities();

      const scanTime = Date.now() - startTime;

      // Update performance metrics
      this.performanceMonitor.updateArbitrageMetrics(
        scanTime,
        routes.length > 0 ? routes.length * 10 : 0, // Estimate pairs processed
        routes.length,
        false // Using workers - would need to check arbitrage strategy
      );

      for (const route of routes) {
        if (route.confidence >= 80) { // Higher threshold for arbitrage
          const success = await this.arbitrageStrategy.executeArbitrage(route);

          if (success) {
            this.state.successfulTrades++;
            this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(route.expectedProfit.toString());
          } else {
            this.state.failedTrades++;
          }
        }
      }

      // Scan for flashloan opportunities using dynamic strategy
      if (this.configService.enableFlashloanAttacks) {
        this.logger.debug('🔍 Scanning with Dynamic Flashloan Strategy...');

        // Check if any strategy is profitable
        const isProfitable = await this.dynamicFlashloanStrategy.isAnyStrategyProfitable();

        if (isProfitable) {
          this.logger.debug('🎯 Profitable opportunities detected, executing best strategy...');

          // Execute best opportunity with MEV protection
          const success = await this.dynamicFlashloanStrategy.executeBestOpportunity();

          if (success) {
            this.state.successfulTrades++;

            this.logger.log('✅ Dynamic flashloan executed successfully');
          } else {
            this.state.failedTrades++;
            this.logger.error('❌ Dynamic flashloan execution failed');
          }
        } else {
          this.logger.debug('📉 No profitable flashloan opportunities found');
        }
      }

      this.updateRiskMetrics();
    } catch (error) {
      this.logger.debug('Error in event-driven arbitrage analysis', {
        trigger,
        error: (error as Error).message
      });
    }
  }

  private setupEventDrivenBlockTracking(): void {
    // Listen for new blocks via websocket events instead of polling
    this.blockEventMonitor.on('newBlock', async (blockData: BlockData) => {
      if (!this.state.isRunning) {
        return;
      }

      try {
        const networkName = this.configService.chainId === 1 ? 'Mainnet' :
                           this.configService.chainId === 11155111 ? 'Sepolia' : 'Testnet';
        this.statusDashboard.updateNetworkStatus(blockData.number, networkName);
      } catch (error) {
        this.logger.debug('Error updating block status', { error: (error as Error).message });
      }
    });

    this.logger.log('Event-driven block tracking started');
  }

  private setupBalanceMonitoring(): void {
    // Update balance immediately
    this.updateEthBalance();

    // Update balance on every new block (event-driven)
    this.blockEventMonitor.on('newBlock', async () => {
      if (this.state.isRunning) {
        await this.updateEthBalance();
      }
    });

    this.logger.log('ETH balance monitoring started');
  }

  private async updateEthBalance(): Promise<void> {
    try {
      const balance = await this.provider.getBalance(this.wallet.address);
      this.statusDashboard.updateEthBalance(balance);

      // Log low balance warning
      if (balance < ethers.parseEther('0.01')) {
        this.logger.warn(`Low ETH balance: ${ethers.formatEther(balance)} ETH - consider adding more funds`);
      }
    } catch (error) {
      this.logger.debug('Error updating ETH balance', { error: (error as Error).message });
    }
  }

  emergencyStop(): void {
    this.logger.warn('🚨 EMERGENCY STOP ACTIVATED');
    this.state.emergencyStop = true;
    this.stop();
    this.eventEmitter.emit('bot.emergencyStop');
  }

  async getWalletInfo(): Promise<{
    address: string;
    balance: string;
    nonce: number;
  }> {
    const balance = await this.wallet.provider!.getBalance(this.wallet.address);
    const nonce = await this.wallet.getNonce();

    return {
      address: this.wallet.address,
      balance: ethers.formatEther(balance),
      nonce
    };
  }
}
