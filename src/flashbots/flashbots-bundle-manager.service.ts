import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';
import { FlashbotsBundleProvider, FlashbotsBundleTransaction } from '@flashbots/ethers-provider-bundle';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';

@Injectable()
export class FlashbotsBundleManagerService {
  private readonly logger = new Logger(FlashbotsBundleManagerService.name);
  
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
    this.wallet = new ethers.Wallet(this.configService.privateKey, this.provider);
    
    if (this.configService.enableFlashbots) {
      this.initializeFlashbotsProvider();
    }
  }

  private async initializeFlashbotsProvider(): Promise<void> {
    try {
      const authSigner = this.configService.flashbotsSignerKey 
        ? new ethers.Wallet(this.configService.flashbotsSignerKey)
        : this.wallet;

      this.flashbotsProvider = await FlashbotsBundleProvider.create(
        this.provider,
        authSigner,
        this.configService.flashbotsRelayUrl
      );

      this.logger.log('✅ Flashbots provider initialized');

    } catch (error) {
      this.logger.error('❌ Failed to initialize Flashbots provider:', error);
      this.flashbotsProvider = null;
    }
  }

  async submitBundle(transactions: FlashbotsBundleTransaction[]): Promise<{ success: boolean; bundleHash?: string; error?: string }> {
    try {
      if (!this.flashbotsProvider) {
        return { success: false, error: 'Flashbots provider not initialized' };
      }

      if (transactions.length === 0) {
        return { success: false, error: 'No transactions in bundle' };
      }

      this.logger.log('📦 Submitting bundle to Flashbots', {
        transactionCount: transactions.length
      });

      // Get target block number
      const currentBlock = await this.provider.getBlockNumber();
      const targetBlock = currentBlock + 1;

      // Submit bundle
      const bundleSubmission = await this.flashbotsProvider.sendBundle(
        transactions,
        targetBlock
      );

      if ('error' in bundleSubmission) {
        return { 
          success: false, 
          error: bundleSubmission.error.message 
        };
      }

      // Wait for bundle inclusion
      const bundleResolution = await bundleSubmission.wait();
      
      if (bundleResolution === 0) {
        this.logger.log('✅ Bundle included in block', {
          bundleHash: bundleSubmission.bundleHash,
          targetBlock
        });
        
        return { 
          success: true, 
          bundleHash: bundleSubmission.bundleHash 
        };
      } else {
        return { 
          success: false, 
          error: 'Bundle not included in target block' 
        };
      }

    } catch (error) {
      this.logger.error('❌ Bundle submission failed:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  async simulateBundle(transactions: FlashbotsBundleTransaction[]): Promise<{ success: boolean; gasUsed?: bigint; error?: string }> {
    try {
      if (!this.flashbotsProvider) {
        return { success: false, error: 'Flashbots provider not initialized' };
      }

      this.logger.debug('🧪 Simulating bundle');

      const currentBlock = await this.provider.getBlockNumber();
      const simulation = await this.flashbotsProvider.simulate(
        transactions,
        currentBlock
      );

      if ('error' in simulation) {
        return { 
          success: false, 
          error: simulation.error.message 
        };
      }

      const totalGasUsed = simulation.results.reduce(
        (total, result) => total + BigInt(result.gasUsed),
        0n
      );

      this.logger.debug('✅ Bundle simulation successful', {
        gasUsed: totalGasUsed.toString()
      });

      return { 
        success: true, 
        gasUsed: totalGasUsed 
      };

    } catch (error) {
      this.logger.error('❌ Bundle simulation failed:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  async getBundleStats(): Promise<any> {
    try {
      if (!this.flashbotsProvider) {
        return null;
      }

      // Get Flashbots stats
      const stats = await this.flashbotsProvider.getUserStats();
      return stats;

    } catch (error) {
      this.logger.error('Error getting bundle stats:', error);
      return null;
    }
  }

  isEnabled(): boolean {
    return this.configService.enableFlashbots && this.flashbotsProvider !== null;
  }

  getProvider(): FlashbotsBundleProvider | null {
    return this.flashbotsProvider;
  }
}
