import { Injectable } from '@nestjs/common';
import chalk from 'chalk';
import { ConfigurationService } from '../config/configuration.service';
import { LogLevel } from '../types';
import { ethers } from 'ethers';

@Injectable()
export class EnhancedLoggerService {
  private startTime: number;

  constructor(private readonly configService: ConfigurationService) {
    this.startTime = Date.now();
  }

  private getTimestamp(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const ms = String(now.getMilliseconds()).padStart(6, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${ms}`;
  }

  private formatCurrency(amount: string | number | bigint, symbol: string = '$'): string {
    const num = typeof amount === 'bigint' ? Number(ethers.formatEther(amount)) : Number(amount);
    return `${num.toFixed(8)} ${symbol}`;
  }

  /**
   * Check if a log level should be shown based on configured log level
   */
  private shouldShowLogLevel(level: LogLevel): boolean {
    const configuredLevel = this.configService.logLevel.toLowerCase();
    const levelHierarchy = {
      'error': 0,
      'warn': 1,
      'info': 2,
      'debug': 3
    };

    const currentLevelValue = levelHierarchy[configuredLevel] ?? 2;
    const messageLevelValue = levelHierarchy[level] ?? 2;

    return messageLevelValue <= currentLevelValue;
  }

  private log(level: LogLevel, message: string, data?: any): void {
    if (!this.shouldShowLogLevel(level)) {
      return;
    }

    const timestamp = this.getTimestamp();
    const uptime = ((Date.now() - this.startTime) / 1000).toFixed(2);
    
    let coloredMessage = message;
    let prefix = '';

    switch (level) {
      case 'error':
        coloredMessage = chalk.red(message);
        prefix = chalk.red('❌ ERROR');
        break;
      case 'warn':
        coloredMessage = chalk.yellow(message);
        prefix = chalk.yellow('⚠️  WARN');
        break;
      case 'info':
        coloredMessage = chalk.blue(message);
        prefix = chalk.blue('ℹ️  INFO');
        break;
      case 'debug':
        coloredMessage = chalk.gray(message);
        prefix = chalk.gray('🔍 DEBUG');
        break;
    }

    const logLine = `${chalk.gray(timestamp)} ${prefix} ${coloredMessage}`;
    
    // Check if split screen dashboard is active
    if (process.env.SPLIT_SCREEN_DASHBOARD === 'true') {
      // Send to dashboard instead of console
      // This would integrate with the split screen dashboard service
      this.sendToDashboard(level, message, data);
    } else {
      console.log(logLine);
      if (data) {
        console.log(chalk.gray(JSON.stringify(data, null, 2)));
      }
    }
  }

  private sendToDashboard(level: LogLevel, message: string, data?: any): void {
    // This would send logs to the split screen dashboard
    // Implementation depends on dashboard service
  }

  // Standard logging methods
  error(message: string, data?: any): void {
    this.log('error', message, data);
  }

  warn(message: string, data?: any): void {
    this.log('warn', message, data);
  }

  info(message: string, data?: any): void {
    this.log('info', message, data);
  }

  debug(message: string, data?: any): void {
    this.log('debug', message, data);
  }

  // Enhanced MEV-specific logging methods
  opportunity(type: string, profit: bigint, confidence: number, data?: any): void {
    const profitStr = this.formatCurrency(profit, 'ETH');
    const message = `${type.toUpperCase()} opportunity found - Profit: ${profitStr}, Confidence: ${confidence}%`;
    this.log('info', chalk.green(`🎯 ${message}`), data);
  }

  execution(type: string, txHash: string, profit?: bigint, data?: any): void {
    const profitStr = profit ? ` - Profit: ${this.formatCurrency(profit, 'ETH')}` : '';
    const message = `${type.toUpperCase()} executed - TX: ${txHash}${profitStr}`;
    this.log('info', chalk.cyan(`⚡ ${message}`), data);
  }

  profit(amount: bigint, type: string, data?: any): void {
    const profitStr = this.formatCurrency(amount, 'ETH');
    const message = `Profit realized: ${profitStr} from ${type}`;
    this.log('info', chalk.green(`💰 ${message}`), data);
  }

  gas(operation: string, gasUsed: bigint, gasPrice: bigint, data?: any): void {
    const gasCost = gasUsed * gasPrice;
    const gasCostEth = this.formatCurrency(gasCost, 'ETH');
    const message = `${operation} - Gas: ${gasUsed.toString()}, Cost: ${gasCostEth}`;
    this.log('debug', chalk.yellow(`⛽ ${message}`), data);
  }

  mempool(message: string, data?: any): void {
    this.log('debug', chalk.magenta(`🔍 MEMPOOL: ${message}`), data);
  }

  flashbots(message: string, data?: any): void {
    this.log('info', chalk.blue(`🚀 FLASHBOTS: ${message}`), data);
  }

  arbitrage(tokenA: string, tokenB: string, profit: bigint, data?: any): void {
    const profitStr = this.formatCurrency(profit, 'ETH');
    const message = `${tokenA}/${tokenB} arbitrage - Profit: ${profitStr}`;
    this.log('info', chalk.cyan(`🔄 ${message}`), data);
  }

  sandwich(victimTx: string, profit: bigint, data?: any): void {
    const profitStr = this.formatCurrency(profit, 'ETH');
    const message = `Sandwich attack on ${victimTx} - Profit: ${profitStr}`;
    this.log('info', chalk.red(`🥪 ${message}`), data);
  }

  frontrun(victimTx: string, profit: bigint, data?: any): void {
    const profitStr = this.formatCurrency(profit, 'ETH');
    const message = `Frontrun attack on ${victimTx} - Profit: ${profitStr}`;
    this.log('info', chalk.yellow(`🏃 ${message}`), data);
  }

  flashloan(provider: string, amount: bigint, profit: bigint, data?: any): void {
    const amountStr = this.formatCurrency(amount, 'ETH');
    const profitStr = this.formatCurrency(profit, 'ETH');
    const message = `${provider} flashloan - Amount: ${amountStr}, Profit: ${profitStr}`;
    this.log('info', chalk.purple(`⚡ ${message}`), data);
  }

  // Performance logging
  performance(operation: string, duration: number, data?: any): void {
    const message = `${operation} completed in ${duration}ms`;
    this.log('debug', chalk.gray(`📊 PERFORMANCE: ${message}`), data);
  }

  // System status logging
  systemStatus(message: string, data?: any): void {
    this.log('info', chalk.blue(`🔧 SYSTEM: ${message}`), data);
  }

  // Network logging
  network(message: string, data?: any): void {
    this.log('debug', chalk.cyan(`🌐 NETWORK: ${message}`), data);
  }

  // Security logging
  security(message: string, data?: any): void {
    this.log('warn', chalk.red(`🔒 SECURITY: ${message}`), data);
  }

  // Bundle logging
  bundle(bundleHash: string, transactions: number, profit: bigint, data?: any): void {
    const profitStr = this.formatCurrency(profit, 'ETH');
    const message = `Bundle ${bundleHash} - ${transactions} txs, Profit: ${profitStr}`;
    this.log('info', chalk.green(`📦 ${message}`), data);
  }

  // Pool logging
  pool(dex: string, tokenA: string, tokenB: string, liquidity: bigint, data?: any): void {
    const liquidityStr = this.formatCurrency(liquidity, 'ETH');
    const message = `${dex} ${tokenA}/${tokenB} pool - Liquidity: ${liquidityStr}`;
    this.log('debug', chalk.blue(`🏊 ${message}`), data);
  }

  // Price logging
  price(token: string, price: number, change: number, data?: any): void {
    const changeStr = change >= 0 ? chalk.green(`+${change.toFixed(2)}%`) : chalk.red(`${change.toFixed(2)}%`);
    const message = `${token} price: $${price.toFixed(6)} (${changeStr})`;
    this.log('debug', chalk.white(`💱 ${message}`), data);
  }

  // Risk logging
  risk(level: 'low' | 'medium' | 'high', message: string, data?: any): void {
    const colors = {
      low: chalk.green,
      medium: chalk.yellow,
      high: chalk.red
    };
    const icons = {
      low: '🟢',
      medium: '🟡',
      high: '🔴'
    };
    
    this.log('warn', `${icons[level]} RISK (${level.toUpperCase()}): ${colors[level](message)}`, data);
  }

  // Success/failure logging
  success(message: string, data?: any): void {
    this.log('info', chalk.green(`✅ SUCCESS: ${message}`), data);
  }

  failure(message: string, data?: any): void {
    this.log('error', chalk.red(`❌ FAILURE: ${message}`), data);
  }

  // Startup banner
  banner(): void {
    const banner = `
${chalk.cyan('╔══════════════════════════════════════════════════════════════╗')}
${chalk.cyan('║')}                    ${chalk.bold.white('Advanced MEV Bot')}                     ${chalk.cyan('║')}
${chalk.cyan('║')}                     ${chalk.gray('NestJS Edition')}                      ${chalk.cyan('║')}
${chalk.cyan('╚══════════════════════════════════════════════════════════════╝')}

${chalk.yellow('🤖 Bot Status:')} ${chalk.green('Initializing...')}
${chalk.yellow('🌐 Network:')} ${this.configService.chainId === 1 ? chalk.blue('Mainnet') : chalk.yellow('Testnet')}
${chalk.yellow('⚡ Strategies:')} ${chalk.cyan('Flashloan, Arbitrage, Sandwich, Frontrun')}
${chalk.yellow('🔧 Mode:')} ${this.configService.dryRun ? chalk.yellow('DRY RUN') : chalk.green('LIVE')}
    `;
    
    console.log(banner);
  }
}
