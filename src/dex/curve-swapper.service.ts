import { Injectable, Logger } from '@nestjs/common';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';

@Injectable()
export class CurveSwapperService {
  private readonly logger = new Logger(CurveSwapperService.name);
  
  private provider: ethers.JsonRpcProvider;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
  }

  async getExchangeRate(
    poolAddress: string,
    tokenIn: number,
    tokenOut: number,
    amountIn: bigint
  ): Promise<bigint> {
    try {
      const poolABI = [
        'function get_dy(int128 i, int128 j, uint256 dx) external view returns (uint256)'
      ];

      const poolContract = new ethers.Contract(poolAddress, poolABI, this.provider);
      const amountOut = await poolContract.get_dy(tokenIn, tokenOut, amountIn);
      
      return BigInt(amountOut.toString());

    } catch (error) {
      this.logger.error('Error getting Curve exchange rate:', error);
      return 0n;
    }
  }

  buildSwapData(
    poolAddress: string,
    tokenIn: number,
    tokenOut: number,
    amountIn: bigint,
    minAmountOut: bigint
  ): string {
    const iface = new ethers.Interface([
      'function exchange(int128 i, int128 j, uint256 dx, uint256 min_dy) external returns (uint256)'
    ]);

    return iface.encodeFunctionData('exchange', [
      tokenIn,
      tokenOut,
      amountIn,
      minAmountOut
    ]);
  }
}
