import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from '../src/app.module';
import { MEVBotService } from '../src/core/mev-bot.service';
import { ConfigurationService } from '../src/config/configuration.service';
import { FlashloanStrategyService } from '../src/strategies/flashloan-strategy.service';
import { ArbitrageStrategyService } from '../src/strategies/arbitrage-strategy.service';
import { SandwichStrategyService } from '../src/strategies/sandwich-strategy.service';
import { FrontrunningStrategyService } from '../src/strategies/frontrunning-strategy.service';
import { MultiBlockStrategyService } from '../src/strategies/multi-block-strategy.service';
import { UniswapV3FlashSwapStrategyService } from '../src/strategies/uniswap-v3-flash-strategy.service';
import { DynamicFlashloanStrategyService } from '../src/strategies/dynamic-flashloan-strategy.service';
import { MEVShareFlashloanStrategyService } from '../src/strategies/mev-share-flashloan-strategy.service';
import { RPCManagerService } from '../src/providers/rpc-manager.service';
import { MempoolMonitorService } from '../src/mempool/mempool-monitor.service';
import { BlockEventMonitorService } from '../src/events/block-event-monitor.service';
import { MEVShareEventMonitorService } from '../src/mev-share/mev-share-event-monitor.service';
import { FlashbotsBundleManagerService } from '../src/flashbots/flashbots-bundle-manager.service';
import { AdvancedGasEstimatorService } from '../src/gas/advanced-gas-estimator.service';
import { GasOptimizerService } from '../src/gas/gas-optimizer.service';
import { FlashbotsExecutorService } from '../src/execution/flashbots-executor.service';
import { BundleSimulatorService } from '../src/simulation/bundle-simulator.service';
import { StatusDashboardService } from '../src/utils/status-dashboard.service';
import { PerformanceMonitorService } from '../src/utils/performance-monitor.service';
import { LoggerService } from '../src/utils/logger.service';
import { EnhancedLoggerService } from '../src/utils/enhanced-logger.service';
import { PoolManagerService } from '../src/dex/pool-manager.service';
import { CurveSwapperService } from '../src/dex/curve-swapper.service';
import { FlashloanContractService } from '../src/contracts/flashloan-contract.service';
import { WorkerManagerService } from '../src/workers/worker-manager.service';
import { ArbitrageWorkerService } from '../src/workers/arbitrage-worker.service';
import { PriceCalculatorService } from '../src/execution/price-calculator.service';
import { SwapDataBuilderService } from '../src/execution/swap-data-builder.service';

describe('MEV Bot NestJS Conversion (e2e)', () => {
  let app: TestingModule;
  let mevBot: MEVBotService;
  let configService: ConfigurationService;

  beforeAll(async () => {
    // Set test environment variables
    process.env.PRIVATE_KEY = '0x' + '1'.repeat(64); // Test private key
    process.env.RPC_URL = 'http://localhost:8545';
    process.env.CHAIN_ID = '31337';
    process.env.DRY_RUN = 'true';
    process.env.ENABLE_FLASHLOAN_ATTACKS = 'true';
    process.env.ENABLE_ARBITRAGE = 'false';
    process.env.ENABLE_SANDWICH_ATTACKS = 'false';
    process.env.ENABLE_FRONT_RUNNING = 'false';
    process.env.ENABLE_MULTI_BLOCK_ATTACKS = 'false';
    process.env.ENABLE_FLASHBOTS = 'false';
    process.env.ENABLE_MEV_SHARE = 'false';

    app = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    mevBot = app.get<MEVBotService>(MEVBotService);
    configService = app.get<ConfigurationService>(ConfigurationService);
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('Application Bootstrap', () => {
    it('should create the application', () => {
      expect(app).toBeDefined();
    });

    it('should have MEVBotService', () => {
      expect(mevBot).toBeDefined();
      expect(mevBot).toBeInstanceOf(MEVBotService);
    });

    it('should have ConfigurationService', () => {
      expect(configService).toBeDefined();
      expect(configService).toBeInstanceOf(ConfigurationService);
    });
  });

  describe('Configuration Service', () => {
    it('should load configuration correctly', () => {
      expect(configService.chainId).toBe(31337);
      expect(configService.dryRun).toBe(true);
      expect(configService.enableFlashloanAttacks).toBe(true);
      expect(configService.enableArbitrage).toBe(false);
    });

    it('should have optimized token pairs', () => {
      const tradingPairs = configService.uniswapV3TradingPairs;
      expect(tradingPairs).toContain('WETH/USDC');
      expect(tradingPairs).toContain('WETH/USDT');
      expect(tradingPairs).toContain('WBTC/WETH');
      expect(tradingPairs).toContain('DAI/USDC');
    });
  });

  describe('Strategy Services', () => {
    it('should have FlashloanStrategyService', () => {
      const service = app.get<FlashloanStrategyService>(FlashloanStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(FlashloanStrategyService);
    });

    it('should have ArbitrageStrategyService', () => {
      const service = app.get<ArbitrageStrategyService>(ArbitrageStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(ArbitrageStrategyService);
    });

    it('should have SandwichStrategyService', () => {
      const service = app.get<SandwichStrategyService>(SandwichStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(SandwichStrategyService);
    });

    it('should have FrontrunningStrategyService', () => {
      const service = app.get<FrontrunningStrategyService>(FrontrunningStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(FrontrunningStrategyService);
    });

    it('should have MultiBlockStrategyService', () => {
      const service = app.get<MultiBlockStrategyService>(MultiBlockStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(MultiBlockStrategyService);
    });

    it('should have UniswapV3FlashSwapStrategyService', () => {
      const service = app.get<UniswapV3FlashSwapStrategyService>(UniswapV3FlashSwapStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(UniswapV3FlashSwapStrategyService);
    });

    it('should have DynamicFlashloanStrategyService', () => {
      const service = app.get<DynamicFlashloanStrategyService>(DynamicFlashloanStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(DynamicFlashloanStrategyService);
    });

    it('should have MEVShareFlashloanStrategyService', () => {
      const service = app.get<MEVShareFlashloanStrategyService>(MEVShareFlashloanStrategyService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(MEVShareFlashloanStrategyService);
    });
  });

  describe('Provider Services', () => {
    it('should have RPCManagerService', () => {
      const service = app.get<RPCManagerService>(RPCManagerService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(RPCManagerService);
    });
  });

  describe('Monitoring Services', () => {
    it('should have MempoolMonitorService', () => {
      const service = app.get<MempoolMonitorService>(MempoolMonitorService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(MempoolMonitorService);
    });

    it('should have BlockEventMonitorService', () => {
      const service = app.get<BlockEventMonitorService>(BlockEventMonitorService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(BlockEventMonitorService);
    });

    it('should have MEVShareEventMonitorService', () => {
      const service = app.get<MEVShareEventMonitorService>(MEVShareEventMonitorService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(MEVShareEventMonitorService);
    });
  });

  describe('Execution Services', () => {
    it('should have FlashbotsExecutorService', () => {
      const service = app.get<FlashbotsExecutorService>(FlashbotsExecutorService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(FlashbotsExecutorService);
    });

    it('should have PriceCalculatorService', () => {
      const service = app.get<PriceCalculatorService>(PriceCalculatorService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(PriceCalculatorService);
    });

    it('should have SwapDataBuilderService', () => {
      const service = app.get<SwapDataBuilderService>(SwapDataBuilderService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(SwapDataBuilderService);
    });
  });

  describe('Gas Services', () => {
    it('should have GasOptimizerService', () => {
      const service = app.get<GasOptimizerService>(GasOptimizerService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(GasOptimizerService);
    });

    it('should have AdvancedGasEstimatorService', () => {
      const service = app.get<AdvancedGasEstimatorService>(AdvancedGasEstimatorService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(AdvancedGasEstimatorService);
    });
  });

  describe('Utility Services', () => {
    it('should have StatusDashboardService', () => {
      const service = app.get<StatusDashboardService>(StatusDashboardService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(StatusDashboardService);
    });

    it('should have PerformanceMonitorService', () => {
      const service = app.get<PerformanceMonitorService>(PerformanceMonitorService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(PerformanceMonitorService);
    });

    it('should have LoggerService', () => {
      const service = app.get<LoggerService>(LoggerService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(LoggerService);
    });

    it('should have EnhancedLoggerService', () => {
      const service = app.get<EnhancedLoggerService>(EnhancedLoggerService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(EnhancedLoggerService);
    });
  });

  describe('MEV Bot Functionality', () => {
    it('should initialize bot state correctly', () => {
      const state = mevBot.getState();
      expect(state).toBeDefined();
      expect(state.isRunning).toBe(false);
      expect(state.totalProfit).toBe(0n);
      expect(state.successfulTrades).toBe(0);
      expect(state.failedTrades).toBe(0);
    });

    it('should get risk metrics', () => {
      const metrics = mevBot.getRiskMetrics();
      expect(metrics).toBeDefined();
      expect(metrics.maxDrawdown).toBe(0n);
      expect(metrics.winRate).toBe(0);
      expect(metrics.averageProfit).toBe(0n);
    });

    it('should get opportunities list', () => {
      const opportunities = mevBot.getOpportunities();
      expect(opportunities).toBeDefined();
      expect(Array.isArray(opportunities)).toBe(true);
    });

    it('should get executed bundles list', () => {
      const bundles = mevBot.getExecutedBundles();
      expect(bundles).toBeDefined();
      expect(Array.isArray(bundles)).toBe(true);
    });
  });

  describe('Performance Optimizations', () => {
    it('should have optimized token configuration', () => {
      expect(configService.enableAllTokenPairs).toBe(false);
      
      const flashloanTokens = configService.flashloanTokens;
      expect(flashloanTokens).toContain('WETH');
      expect(flashloanTokens).toContain('USDC');
      expect(flashloanTokens).toContain('USDT');
      expect(flashloanTokens).toContain('DAI');
      expect(flashloanTokens).toContain('WBTC');
    });

    it('should have optimized Uniswap V3 configuration', () => {
      const tradingPairs = configService.uniswapV3TradingPairs;
      expect(tradingPairs).toHaveLength(4);
      
      const feeTiers = configService.uniswapV3FeeTiers;
      expect(feeTiers).toContain(500);
      expect(feeTiers).toContain(3000);
      expect(feeTiers).toContain(10000);
    });
  });
});
