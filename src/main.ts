import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  // Initialize split-screen dashboard early if enabled
  if (process.env.SPLIT_SCREEN_DASHBOARD === 'true') {
    const { splitScreenDashboard } = await import('./utils/splitScreenDashboard');
    
    // Start split screen dashboard immediately to capture all logs
    splitScreenDashboard.start();
    
    // Give it a moment to initialize
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  const logger = new Logger('Bootstrap');
  logger.log('🤖 Initializing Advanced MEV Bot with NestJS...');

  try {
    const app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    const configService = app.get(ConfigService);
    
    // Enable graceful shutdown
    app.enableShutdownHooks();

    // Handle graceful shutdown with improved signal handling
    let isShuttingDown = false;

    const gracefulShutdown = async (signal: string) => {
      if (isShuttingDown) {
        logger.warn(`Already shutting down, ignoring ${signal}`);
        return;
      }

      isShuttingDown = true;
      logger.log(`Received ${signal}, shutting down gracefully...`);

      try {
        await Promise.race([
          app.close(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Shutdown timeout')), 10000)
          )
        ]);
        logger.log('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('❌ Shutdown error, forcing exit:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('UncaughtException:', error);
      if (!isShuttingDown) {
        process.exit(1);
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', { promise, reason });
      if (!isShuttingDown) {
        process.exit(1);
      }
    });

    // Get the MEV Bot service and start it
    const { MEVBotService } = await import('./core/mev-bot.service');
    const mevBot = app.get(MEVBotService);
    
    await mevBot.start();

    logger.log('🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.');

  } catch (error) {
    logger.error('Failed to start MEV Bot:', error);
    
    // Fallback to simple bot if advanced bot fails
    logger.warn('⚠️  Advanced MEV Bot failed, falling back to Simple Bot...');
    try {
      const { runSimpleBot } = await import('./simple-bot');
      await runSimpleBot();
    } catch (fallbackError) {
      logger.error('Fallback failed:', fallbackError);
      process.exit(1);
    }
  }
}

// Start the application
bootstrap().catch((error) => {
  console.error('Bootstrap failed:', error);
  process.exit(1);
});
