import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ethers } from 'ethers';
import { ConfigurationService } from '../config/configuration.service';
import { RPCManagerService } from '../providers/rpc-manager.service';

@Injectable()
export class BlockEventMonitorService {
  private readonly logger = new Logger(BlockEventMonitorService.name);
  
  private provider: ethers.Provider;
  private isRunning = false;
  private currentBlock = 0;

  constructor(
    private readonly configService: ConfigurationService,
    private readonly rpcManager: RPCManagerService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.provider = this.rpcManager.getHttpProvider();
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Block event monitor is already running');
      return;
    }

    this.logger.log('📦 Starting block event monitoring...');

    try {
      // Get current block number
      this.currentBlock = await this.provider.getBlockNumber();
      
      // Listen for new blocks
      this.provider.on('block', this.handleNewBlock.bind(this));
      
      this.isRunning = true;
      this.logger.log('✅ Block event monitoring started');
      this.eventEmitter.emit('blockMonitor.started');

    } catch (error) {
      this.logger.error('❌ Failed to start block event monitoring:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Block event monitor is not running');
      return;
    }

    this.logger.log('🛑 Stopping block event monitoring...');

    try {
      this.provider.removeAllListeners('block');
      this.isRunning = false;
      
      this.logger.log('✅ Block event monitoring stopped');
      this.eventEmitter.emit('blockMonitor.stopped');

    } catch (error) {
      this.logger.error('❌ Error stopping block event monitoring:', error);
      throw error;
    }
  }

  private async handleNewBlock(blockNumber: number): Promise<void> {
    try {
      if (!this.isRunning) return;

      this.currentBlock = blockNumber;
      
      // Get block details
      const block = await this.provider.getBlock(blockNumber);
      if (!block) {
        this.logger.warn(`Failed to get block ${blockNumber}`);
        return;
      }

      this.logger.debug(`📦 New block: ${blockNumber} (${block.transactions.length} txs)`);

      // Emit block event
      this.eventEmitter.emit('block.new', {
        number: blockNumber,
        hash: block.hash,
        timestamp: block.timestamp,
        gasUsed: block.gasUsed,
        gasLimit: block.gasLimit,
        baseFeePerGas: block.baseFeePerGas,
        transactionCount: block.transactions.length
      });

      // Check for MEV opportunities in the new block
      await this.analyzeBlockForMEV(block);

    } catch (error) {
      this.logger.error(`Error handling new block ${blockNumber}:`, error);
    }
  }

  private async analyzeBlockForMEV(block: ethers.Block): Promise<void> {
    try {
      // Analyze block for MEV patterns
      const mevAnalysis = {
        blockNumber: block.number,
        timestamp: block.timestamp,
        gasUsed: block.gasUsed,
        baseFee: block.baseFeePerGas,
        transactionCount: block.transactions.length,
        potentialMEV: false,
        patterns: []
      };

      // Check for high gas usage (potential MEV activity)
      if (block.gasUsed && block.gasLimit) {
        const gasUtilization = Number(block.gasUsed) / Number(block.gasLimit);
        if (gasUtilization > 0.8) {
          mevAnalysis.potentialMEV = true;
          mevAnalysis.patterns.push('high-gas-utilization');
        }
      }

      // Check for unusual transaction count
      if (block.transactions.length > 200) {
        mevAnalysis.potentialMEV = true;
        mevAnalysis.patterns.push('high-transaction-count');
      }

      // Emit MEV analysis
      if (mevAnalysis.potentialMEV) {
        this.eventEmitter.emit('block.mevDetected', mevAnalysis);
      }

    } catch (error) {
      this.logger.error('Error analyzing block for MEV:', error);
    }
  }

  getCurrentBlock(): number {
    return this.currentBlock;
  }

  getStatus(): { isRunning: boolean; currentBlock: number } {
    return {
      isRunning: this.isRunning,
      currentBlock: this.currentBlock
    };
  }
}
